# Copyright (c) OpenMMLab. All rights reserved.
import os
import sys
import random
import time
from pathlib import Path
from argparse import ArgumentParser

import cv2
import mmcv
import numpy as np
import torch
from PIL import Image
from typing import List, Tuple

# Custom module imports
sys.path.append(str(Path(__file__).resolve().parents[3]))
sys.path.append('/home/<USER>/panpan/mm_detection/test_carm')

from projects.easydeploy.model import ORTWrapper  # noqa: E402
from utils import (  # noqa: E402
    bbox_postprocess, 
    preprocess, 
    visualize_detections,
    reg_max2bbox,
    resize_and_pad
)
from decode import (  # noqa: E402
    predict_by_feat,
    _bbox_post_process,
    get_single_pred,
    
)
from easydict import EasyDict
from pixel_to_physical_py.src.detection_processor import DetectionProcessor, DetectionResult, BBox

# Configuration and Constants
COLORS = [[random.randint(0, 255) for _ in range(3)] for _ in range(1000)]
CLASS_NAMES = ['bin', 'cloth', 'rug', 'shoe', 'wire', 'rail', 'wheel']
def transform_point(point, scale_factor, pad_param):
    """
    将模型输出的点坐标转换到原始图像坐标系
    Args:
        point: 元组 (x, y, conf)
        scale_factor: 缩放因子（原图尺寸 / 模型输入尺寸）
        pad_param: 填充参数 [top, bottom, left, right]
    Returns:
        Tuple: 转换后的坐标 (x_actual, y_actual, conf)
    """
    x, y, conf = point
    top, bottom, left, right = pad_param
    
    # 去除填充并缩放
    x_actual = (x - left) / scale_factor
    y_actual = (y - top)  / scale_factor
    
    return (x_actual, y_actual, conf)
def parse_args():
    parser = ArgumentParser()
    parser.add_argument('--camera-id', type=int, default=0, help='Camera device ID (default: 0)')
    parser.add_argument('--frame-size', nargs=2, type=int, default=[1280, 720],
                        help='Frame processing size [width, height] (default: 1280 720)')
    parser.add_argument('--config', default="yolov8s_old7cls_640.py",
                        help='Main config file')
    parser.add_argument('--checkpoint', '-c', default="yolov8v6_only.onnx",
                        help='Main checkpoint file')
    parser.add_argument('--line_config', default="line_in_five_head.py",
                        help='Line detection config file')
    parser.add_argument('--line_checkpoint', '-l', default="yolov8v13_only.onnx",
                        help='Line detection checkpoint file')
    parser.add_argument('--out-dir', default='camera_output',
                        help='Path to output file (unused in camera mode)')
    parser.add_argument('--device', default='cuda:0',
                        help='Device used for inference')
    parser.add_argument('--fps-display', action='store_true',
                        help='Show FPS on output')
    parser.add_argument('--outlier-mode', choices=['filter', 'correct'], default='filter',
                        help='异常点处理模式: filter=删除异常点, correct=修正异常点距离 (default: correct)')
    return parser.parse_args()

def load_model(checkpoint_path, device):
    if checkpoint_path.endswith('.onnx'):
        model = ORTWrapper(checkpoint_path, device)
    model.to(device)
    return model

def build_preprocessing_pipeline():
    return {
        'pipeline': resize_and_pad,
        'preprocessor': preprocess(),
    }

def preprocess_image(rgb_img, pipeline, preprocessor, device):
    data_dict = dict(img=rgb_img, img_id=0)
    processed = pipeline(rgb_img)
    data, scale,pad_param= processed
    scale = 1.0/scale
    data = preprocessor(data).to(device)
    samples ={"scale":scale,"pad_param":pad_param}

    return data, samples

def process_detection_results(feats, device, test_cfg, original_shape, scale_factor, pad_param):
    bbox_preds = []
    cls_scores = []
    
    for feat in feats:
        bbox_pred, cls_score = torch.split(feat, [64, 7], dim=1)
        bbox_preds.append(bbox_pred)
        cls_scores.append(cls_score)
    
    if bbox_preds[0].shape[1] == 64:
        proj = torch.arange(16, dtype=torch.float).to(device)
        bbox_preds = [reg_max2bbox(bbox, proj) for bbox in bbox_preds]
    # print(original_shape)
    batch_img_metas = [{
        'ori_shape': original_shape,
        'pad_shape': (640, 640, 3),
        'scale_factor': scale_factor,
        "pad_param": pad_param
    }]
    
    return predict_by_feat(
        cls_scores, 
        bbox_preds, 
        objectnesses=None,
        batch_img_metas=batch_img_metas,
        cfg=test_cfg,
        post_processing=_bbox_post_process
    )

def _preprocess_image(img: np.ndarray, calibration_mapx_path, calibration_mapy_path) -> np.ndarray:
        """预处理图像（去畸变）"""
        
        calibration_mapx = np.fromfile(calibration_mapx_path, 
                    dtype=np.float32).reshape(img.shape[0], img.shape[1])
        calibration_mapy = np.fromfile(calibration_mapy_path, 
                    dtype=np.float32).reshape(img.shape[0], img.shape[1])
        processed_img = cv2.remap(img, calibration_mapx, calibration_mapy, cv2.INTER_LINEAR)
        print("使用标定映射表处理图像")
        
            
        return processed_img

def _initialize_physical_processor() -> None:
        """初始化物理距离处理器"""
        # with open(self.config.size_ranges_config, 'r') as f:
        #     size_config = yaml.safe_load(f)

        physical_processor = DetectionProcessor()
        # physical_processor.set_size_ranges_config(size_config)
        return physical_processor

def _convert_points_to_detection_results(points_data: List[Tuple], point_size: int = 10) -> List[DetectionResult]:
        """
        将点检测结果转换为DetectionResult列表

        Args:
            points_data: 点检测结果列表，每个元素为 (x_actual, y_actual, conf)
            point_size: 点周围的边界框大小（像素）

        Returns:
            转换后的检测结果列表
        """
        detection_results = []

        if not points_data or len(points_data) == 0:
            return detection_results

        for i, (x_actual, y_actual, conf) in enumerate(points_data):
            # 为点创建一个小的边界框用于测距计算
            bbox_obj = BBox(
                x1=int(x_actual),
                y1=int(y_actual ),
                x2=int(x_actual ),
                y2=int(y_actual )
            )

            # 创建DetectionResult对象
            det_result = DetectionResult(
                bbox=bbox_obj,
                label=f"point_{i}",  # 给点一个标签
                confidence=float(conf)
            )

            detection_results.append(det_result)

        return detection_results

def process_wire_distance_outliers(valid_results: List[Tuple], outlier_mode: str = 'correct',
                                 max_distance_jump: float = 15.0, smoothing_window: int = 3) -> List[Tuple]:
    """
    处理wire检测点的距离异常值

    Args:
        valid_results: 包含(DetectionResult, point_coords)的列表
        outlier_mode: 处理模式 'filter'=删除异常点, 'correct'=修正异常点距离
        max_distance_jump: 最大允许的距离跳跃值(cm)
        smoothing_window: 平滑窗口大小

    Returns:
        处理后的结果列表
    """
    if len(valid_results) < 3:
        print("检测点数量不足，跳过异常值处理")
        return valid_results

    # 按照x坐标从左到右排序
    # sorted_results = sorted(valid_results, key=lambda x: x[1][0])  # x[1][0] 是 point_coords 的 x 坐标
    
    # 按照x坐标从右到左排序（x坐标从大到小）
    # sorted_results = sorted(valid_results, key=lambda x: x[1][0], reverse=True)
    # distances = [result[0].physical_distance for result in sorted_results]
    # print(f"原始距离序列: {[f'{d:.1f}' for d in distances]}")
    
    sorted_results = valid_results
    distances = [result[0].physical_distance for result in valid_results]
    print(f"原始距离序列: {[f'{d:.1f}' for d in distances]}")
    # 检测异常点
    outlier_indices = _detect_distance_outliers(distances, max_distance_jump)

    if not outlier_indices:
        print("未检测到距离异常点")
        return sorted_results

    print(f"检测到异常点索引: {outlier_indices}")

    if outlier_mode == 'filter':
        # 模式1: 删除异常点
        filtered_results = []
        for i, result in enumerate(sorted_results):
            if i not in outlier_indices:
                filtered_results.append(result)
            else:
                print(f"删除异常点 {i}: 距离={result[0].physical_distance:.1f}cm")

        print(f"过滤后保留 {len(filtered_results)}/{len(sorted_results)} 个点")
        return filtered_results

    elif outlier_mode == 'correct':
        # 模式2: 修正异常点距离
        corrected_results = []
        corrected_distances = _correct_outlier_distances(distances, outlier_indices, smoothing_window)

        print(f"\n=== 距离修正详情 ===")
        correction_count = 0

        for i, (result, corrected_distance) in enumerate(zip(sorted_results, corrected_distances)):
            det_result, point_coords = result

            if i in outlier_indices:
                original_distance = det_result.physical_distance
                print(f"修正异常点 {i}: {original_distance:.1f}cm -> {corrected_distance:.1f}cm (差异: {corrected_distance - original_distance:+.1f}cm)")
                correction_count += 1

                # 创建新的DetectionResult对象，更新距离
                corrected_det = DetectionResult(
                    bbox=det_result.bbox,
                    label=det_result.label,
                    confidence=det_result.confidence,
                    physical_distance=corrected_distance,
                    left_distance=corrected_distance,
                    right_distance=corrected_distance,
                    length=det_result.length,
                    des=det_result.des + " [距离已修正]"
                )
                corrected_results.append((corrected_det, point_coords))
            else:
                print(f"保持正常点 {i}: {det_result.physical_distance:.1f}cm (无需修正)")
                corrected_results.append(result)

        print(f"总计修正了 {correction_count} 个异常点")
        return corrected_results

    return sorted_results

def _detect_distance_outliers(distances: List[float], max_distance_jump: float) -> List[int]:
    """
    检测距离序列中的异常串点
    异常串点特征：
    1. 距离值较大
    2. 比前后正常串的距离值大

    Args:
        distances: 距离序列
        max_distance_jump: 最大允许的距离跳跃值

    Returns:
        异常点的索引列表
    """
    outlier_indices = []

    if len(distances) < 3:
        return outlier_indices

    # 步骤1: 识别距离串（连续相似距离的点群）
    # 使用自适应阈值进行分串，能更好地检测局部异常
    segmentation_threshold = min(5.0, max_distance_jump)  # 自适应阈值
    distance_segments = _identify_distance_segments(distances, segmentation_threshold)
    print(f"识别到 {len(distance_segments)} 个距离串 (阈值: {segmentation_threshold}cm)")

    # 步骤2: 分析每个串的特征
    segment_stats = []
    for i, segment in enumerate(distance_segments):
        start_idx, end_idx = segment
        segment_distances = distances[start_idx:end_idx+1]
        avg_distance = np.mean(segment_distances)
        max_distance = np.max(segment_distances)
        min_distance = np.min(segment_distances)
        segment_length = end_idx - start_idx + 1

        segment_stats.append({
            'start': start_idx,
            'end': end_idx,
            'avg_distance': avg_distance,
            'max_distance': max_distance,
            'min_distance': min_distance,
            'length': segment_length,
            'distances': segment_distances
        })
        print(f"串 {i}: 索引[{start_idx}:{end_idx}], 长度={segment_length}, "
              f"平均={avg_distance:.1f}cm, 范围=[{min_distance:.1f}, {max_distance:.1f}]cm")

    # 步骤3: 识别异常串
    if len(segment_stats) >= 2:
        # 计算全局距离统计
        all_distances = np.array(distances)
        global_median = np.median(all_distances)
        global_std = np.std(all_distances)

        print(f"全局统计: 中位数={global_median:.1f}cm, 标准差={global_std:.1f}cm")

        # 改进的异常检测逻辑
        for i, segment in enumerate(segment_stats):
            is_outlier_segment = False

            # 条件1: 距离值明显偏大（更严格的阈值）
            distance_threshold = global_median + 2.0 * global_std  # 更严格的阈值
            if segment['avg_distance'] > distance_threshold:
                print(f"串 {i} 距离异常偏大: {segment['avg_distance']:.1f} > {distance_threshold:.1f}")
                is_outlier_segment = True

            # 条件2: 相对于其他串明显偏高
            if not is_outlier_segment and len(segment_stats) >= 3:
                # 计算其他串的平均距离
                other_segments_distances = []
                for j, other_segment in enumerate(segment_stats):
                    if j != i:
                        other_segments_distances.append(other_segment['avg_distance'])

                if other_segments_distances:
                    other_median = np.median(other_segments_distances)

                    # 如果当前串比其他串的中位数高出很多
                    if segment['avg_distance'] - other_median > max_distance_jump:
                        print(f"串 {i} 相对其他串偏高: 当前={segment['avg_distance']:.1f}, "
                              f"其他串中位数={other_median:.1f}, 差异={segment['avg_distance'] - other_median:.1f}")
                        is_outlier_segment = True

            # 条件3: 检查是否为孤立的高距离串（前后串都明显更低）
            if not is_outlier_segment:
                prev_segment_avg = None
                next_segment_avg = None

                if i > 0:
                    prev_segment_avg = segment_stats[i-1]['avg_distance']
                if i < len(segment_stats) - 1:
                    next_segment_avg = segment_stats[i+1]['avg_distance']

                # 检查是否为孤立的高峰
                is_isolated_peak = False
                if prev_segment_avg is not None and next_segment_avg is not None:
                    # 前后都有串，检查是否明显高于前后
                    if (segment['avg_distance'] - prev_segment_avg > max_distance_jump and
                        segment['avg_distance'] - next_segment_avg > max_distance_jump):
                        is_isolated_peak = True
                elif prev_segment_avg is not None:
                    # 只有前串，检查是否明显高于前串
                    if segment['avg_distance'] - prev_segment_avg > max_distance_jump * 1.5:
                        is_isolated_peak = True
                elif next_segment_avg is not None:
                    # 只有后串，检查是否明显高于后串
                    if segment['avg_distance'] - next_segment_avg > max_distance_jump * 1.5:
                        is_isolated_peak = True

                if is_isolated_peak:
                    prev_str = f"{prev_segment_avg:.1f}" if prev_segment_avg is not None else "None"
                    next_str = f"{next_segment_avg:.1f}" if next_segment_avg is not None else "None"
                    print(f"串 {i} 为孤立高峰: 当前={segment['avg_distance']:.1f}, "
                          f"前={prev_str}, 后={next_str}")
                    is_outlier_segment = True

            # 如果是异常串，添加所有点到异常列表
            if is_outlier_segment:
                for idx in range(segment['start'], segment['end'] + 1):
                    outlier_indices.append(idx)
                print(f"标记串 {i} 为异常串: 索引 [{segment['start']}:{segment['end']}]")

        # 如果没有检测到异常串，但有明显的距离分层，选择最高的串作为异常
        if not outlier_indices and len(segment_stats) >= 2:
            # 找到平均距离最高的串
            max_avg_distance = max(seg['avg_distance'] for seg in segment_stats)
            min_avg_distance = min(seg['avg_distance'] for seg in segment_stats)

            # 如果最高串与最低串差异很大，标记最高串为异常
            if max_avg_distance - min_avg_distance > max_distance_jump * 2:
                for i, segment in enumerate(segment_stats):
                    if segment['avg_distance'] == max_avg_distance:
                        for idx in range(segment['start'], segment['end'] + 1):
                            outlier_indices.append(idx)
                        print(f"基于距离分层标记串 {i} 为异常串: 最高距离={max_avg_distance:.1f}, "
                              f"最低距离={min_avg_distance:.1f}, 差异={max_avg_distance - min_avg_distance:.1f}")
                        break

    # 步骤4: 检测串内的局部异常点（孤立的高距离点）
    local_outliers = _detect_local_outliers(distances, max_distance_jump)
    if local_outliers:
        print(f"检测到 {len(local_outliers)} 个局部异常点: {local_outliers}")
        outlier_indices.extend(local_outliers)

    # 步骤5: 使用梯度变化验证异常点
    gradient_outliers = _detect_gradient_outliers(distances, max_distance_jump)
    if gradient_outliers:
        print(f"检测到 {len(gradient_outliers)} 个梯度异常点: {gradient_outliers}")

    # 步骤6: 智能组合多种检测方法
    final_outliers = _combine_detection_methods(
        outlier_indices, local_outliers, gradient_outliers, distances, max_distance_jump
    )

    return sorted(list(set(final_outliers)))

def _detect_local_outliers(distances: List[float], threshold: float) -> List[int]:
    """
    检测局部异常点（在序列中突然跳高的孤立点或小段）

    Args:
        distances: 距离序列
        threshold: 异常检测阈值

    Returns:
        局部异常点的索引列表
    """
    if len(distances) < 3:
        return []

    outliers = []

    # 方法1: 检测孤立的高峰点
    for i in range(1, len(distances) - 1):
        curr_dist = distances[i]
        prev_dist = distances[i-1]
        next_dist = distances[i+1]

        # 如果当前点比前后点都高很多
        if (curr_dist - prev_dist > threshold and
            curr_dist - next_dist > threshold):
            outliers.append(i)
            print(f"检测到孤立高峰点 {i}: {curr_dist:.1f}cm (前:{prev_dist:.1f}, 后:{next_dist:.1f})")

    # 方法2: 检测连续的高距离段（相对于周围环境）- 更严格的条件
    window_size = 4  # 增大窗口
    for i in range(window_size, len(distances) - window_size):
        # 计算当前点周围的局部环境
        before_window = distances[i-window_size:i]
        after_window = distances[i+1:i+1+window_size]
        local_env = before_window + after_window

        if local_env and len(local_env) >= 6:  # 确保有足够的环境数据
            local_median = np.median(local_env)
            local_std = np.std(local_env)
            curr_dist = distances[i]

            # 更严格的条件：既要比中位数高，也要超过局部标准差
            if (curr_dist - local_median > threshold * 1.2 and
                curr_dist - local_median > local_std * 2):
                if i not in outliers:  # 避免重复添加
                    outliers.append(i)
                    print(f"检测到局部异常点 {i}: {curr_dist:.1f}cm (局部环境中位数:{local_median:.1f}, 标准差:{local_std:.1f})")

    # 方法3: 检测相对于全局基线的异常点 - 仅用于验证
    # 计算移动中位数作为基线
    baseline = []
    window = 7  # 增大窗口
    for i in range(len(distances)):
        start = max(0, i - window//2)
        end = min(len(distances), i + window//2 + 1)
        window_data = distances[start:end]
        baseline.append(np.median(window_data))

    # 只检测明显的异常点
    global_std = np.std(distances)
    for i, (dist, base) in enumerate(zip(distances, baseline)):
        if (dist - base > threshold * 1.8 and  # 更严格的阈值
            dist - base > global_std * 1.5):   # 同时考虑全局标准差
            if i not in outliers:
                outliers.append(i)
                print(f"检测到基线异常点 {i}: {dist:.1f}cm (基线:{base:.1f}, 差异:{dist-base:.1f})")

    return sorted(outliers)
import math
def sort_points_by_line(points):
    """
    按线条方向重新排序像素坐标点
    :param points: 包含(x,y)坐标的列表，格式可以是元组或列表
    :return: 按线条方向排序后的坐标列表
    """
    if not points:
        return []
    
    # 转换为(x,y)元组列表（如果输入是其他格式）
    coords = [(float(p[0]), float(p[1]), float(p[2])) if isinstance(p, (list, tuple)) else p for p in points]
    
    # 选择最上方的点作为起始点（y最小）
    start_point = max(coords, key=lambda p: (p[0], p[1]))
    sorted_points = [start_point]
    remaining_points = coords.copy()
    remaining_points.remove(start_point)
    
    while remaining_points:
        last_point = sorted_points[-1]
        # 找到离last_point最近的点
        nearest_point = min(remaining_points, 
                          key=lambda p: math.sqrt((p[0]-last_point[0])**2 + (p[1]-last_point[1])**2))
        sorted_points.append(nearest_point)
        remaining_points.remove(nearest_point)
    
    return sorted_points


def _detect_gradient_outliers(distances: List[float], threshold: float) -> List[int]:
    """
    基于距离梯度变化检测异常点
    只有当距离梯度变化大的时候才认为是异常

    Args:
        distances: 距离序列
        threshold: 梯度变化阈值

    Returns:
        梯度异常点的索引列表
    """
    if len(distances) < 5:
        return []

    outliers = []

    # 计算一阶梯度（相邻点的距离差）
    gradients = []
    for i in range(1, len(distances)):
        grad = distances[i] - distances[i-1]
        gradients.append(grad)

    # 计算二阶梯度（梯度的变化率）
    gradient_changes = []
    for i in range(1, len(gradients)):
        grad_change = abs(gradients[i] - gradients[i-1])
        gradient_changes.append(grad_change)

    print(f"梯度变化分析:")
    print(f"最大梯度变化: {max(gradient_changes):.1f}cm")
    print(f"平均梯度变化: {np.mean(gradient_changes):.1f}cm")
    print(f"梯度变化阈值: {threshold:.1f}cm")

    # 检测梯度变化异常的点
    for i, grad_change in enumerate(gradient_changes):
        if grad_change > threshold:
            # 梯度变化异常的点索引（对应原始距离序列的索引）
            point_idx = i + 1  # 因为梯度变化从第2个梯度开始计算

            # 进一步验证：检查是否为真正的异常跳跃
            if point_idx < len(distances) - 1:
                # 检查前后距离差异
                prev_dist = distances[point_idx - 1]
                curr_dist = distances[point_idx]
                next_dist = distances[point_idx + 1]

                # 如果当前点与前后点的距离差都很大，认为是异常
                diff_prev = abs(curr_dist - prev_dist)
                diff_next = abs(curr_dist - next_dist)

                if diff_prev > threshold * 0.8 or diff_next > threshold * 0.8:
                    outliers.append(point_idx)
                    print(f"梯度异常点 {point_idx}: 梯度变化={grad_change:.1f}, "
                          f"距离={curr_dist:.1f} (前:{prev_dist:.1f}, 后:{next_dist:.1f})")

    # 使用滑动窗口检测连续的梯度异常
    window_size = 3
    for i in range(len(gradient_changes) - window_size + 1):
        window_changes = gradient_changes[i:i + window_size]
        avg_change = np.mean(window_changes)

        if avg_change > threshold * 0.7:  # 稍微宽松的阈值
            # 检查窗口内的点
            for j in range(window_size):
                point_idx = i + j + 1
                if point_idx not in outliers and point_idx < len(distances):
                    # 验证这个点是否真的异常
                    curr_dist = distances[point_idx]

                    # 计算局部环境的中位数
                    start = max(0, point_idx - 2)
                    end = min(len(distances), point_idx + 3)
                    local_env = distances[start:point_idx] + distances[point_idx+1:end]

                    if local_env:
                        local_median = np.median(local_env)
                        if abs(curr_dist - local_median) > threshold * 0.6:
                            outliers.append(point_idx)
                            print(f"连续梯度异常点 {point_idx}: 窗口平均变化={avg_change:.1f}, "
                                  f"距离={curr_dist:.1f}, 局部中位数={local_median:.1f}")

    return sorted(list(set(outliers)))

def _combine_detection_methods(segment_outliers: List[int], local_outliers: List[int],
                              gradient_outliers: List[int], distances: List[float],
                              threshold: float) -> List[int]:
    """
    智能组合多种异常检测方法的结果

    Args:
        segment_outliers: 串级异常点索引
        local_outliers: 局部异常点索引
        gradient_outliers: 梯度异常点索引
        distances: 距离序列
        threshold: 异常阈值

    Returns:
        最终的异常点索引列表
    """
    print(f"\n=== 智能组合异常检测结果 ===")

    # 高置信度异常点：被多种方法同时检测到
    high_confidence = []

    # 中等置信度异常点：被梯度检测到，且满足其他条件
    medium_confidence = []

    # 所有候选异常点
    all_candidates = set(segment_outliers + local_outliers + gradient_outliers)

    for idx in all_candidates:
        detection_count = 0
        detection_methods = []

        if idx in segment_outliers:
            detection_count += 1
            detection_methods.append("串级")
        if idx in local_outliers:
            detection_count += 1
            detection_methods.append("局部")
        if idx in gradient_outliers:
            detection_count += 1
            detection_methods.append("梯度")

        # 高置信度：被2种或以上方法检测到
        if detection_count >= 2:
            high_confidence.append(idx)
            print(f"高置信度异常点 {idx}: 被{detection_methods}方法检测到")

        # 中等置信度：只被梯度方法检测到，但梯度变化确实很大
        elif idx in gradient_outliers and detection_count == 1:
            # 验证梯度变化是否足够大
            if _verify_gradient_anomaly(idx, distances, threshold):
                medium_confidence.append(idx)
                print(f"中等置信度异常点 {idx}: 梯度变化显著")

    # 检测连续的异常段
    continuous_segments = _detect_continuous_anomaly_segments(distances, threshold)
    if continuous_segments:
        print(f"检测到连续异常段: {continuous_segments}")
        for start, end in continuous_segments:
            for idx in range(start, end + 1):
                if idx not in high_confidence and idx not in medium_confidence:
                    medium_confidence.append(idx)
                    print(f"连续段异常点 {idx}: 属于异常段[{start}:{end}]")

    # 合并结果
    final_outliers = high_confidence + medium_confidence

    print(f"最终异常点: 高置信度{len(high_confidence)}个, 中等置信度{len(medium_confidence)}个")
    print(f"总计: {sorted(final_outliers)}")

    return final_outliers

def _verify_gradient_anomaly(idx: int, distances: List[float], threshold: float) -> bool:
    """验证梯度异常的严重程度"""
    if idx <= 0 or idx >= len(distances) - 1:
        return False

    prev_dist = distances[idx - 1]
    curr_dist = distances[idx]
    next_dist = distances[idx + 1]

    # 检查距离跳跃是否足够大
    jump_prev = abs(curr_dist - prev_dist)
    jump_next = abs(curr_dist - next_dist)

    return jump_prev > threshold * 0.8 or jump_next > threshold * 0.8

def _detect_continuous_anomaly_segments(distances: List[float], threshold: float) -> List[Tuple[int, int]]:
    """检测连续的异常段"""
    if len(distances) < 5:
        return []

    segments = []

    # 计算全局基线（移动中位数）
    baseline = []
    window = 7
    for i in range(len(distances)):
        start = max(0, i - window//2)
        end = min(len(distances), i + window//2 + 1)
        window_data = distances[start:end]
        baseline.append(np.median(window_data))

    # 寻找连续高于基线的段
    current_segment_start = None

    for i, (dist, base) in enumerate(zip(distances, baseline)):
        is_anomaly = dist - base > threshold * 0.5  # 相对宽松的阈值

        if is_anomaly:
            if current_segment_start is None:
                current_segment_start = i
        else:
            if current_segment_start is not None:
                # 结束当前段
                if i - current_segment_start >= 3:  # 至少3个连续点
                    segments.append((current_segment_start, i - 1))
                current_segment_start = None

    # 处理最后一个段
    if current_segment_start is not None:
        if len(distances) - current_segment_start >= 3:
            segments.append((current_segment_start, len(distances) - 1))

    return segments

def _identify_distance_segments(distances: List[float], similarity_threshold: float) -> List[Tuple[int, int]]:
    """
    识别距离序列中的连续相似距离串

    Args:
        distances: 距离序列
        similarity_threshold: 相似性阈值

    Returns:
        距离串的列表，每个元素为 (start_index, end_index)
    """
    if len(distances) < 2:
        return [(0, len(distances)-1)] if distances else []

    segments = []
    current_start = 0

    for i in range(1, len(distances)):
        # 检查当前点与前一点的距离差异
        distance_diff = abs(distances[i] - distances[i-1])

        # 如果差异超过阈值，结束当前串，开始新串
        if distance_diff > similarity_threshold:
            segments.append((current_start, i-1))
            current_start = i

    # 添加最后一个串
    segments.append((current_start, len(distances)-1))

    # 合并过短的串（长度为1的串与相邻串合并）
    merged_segments = []
    i = 0
    while i < len(segments):
        start, end = segments[i]

        # 如果当前串只有1个点，尝试与相邻串合并
        if end - start == 0 and len(merged_segments) > 0:
            # 与前一个串合并
            prev_start, _ = merged_segments[-1]
            merged_segments[-1] = (prev_start, end)
        else:
            merged_segments.append((start, end))
        i += 1

    return merged_segments

def _correct_outlier_distances(distances: List[float], outlier_indices: List[int],
                              smoothing_window: int) -> List[float]:
    """
    分层修正异常串点的距离值
    先剔除误差最大的串，再用正常串修正其他异常串

    Args:
        distances: 原始距离序列
        outlier_indices: 异常点索引
        smoothing_window: 平滑窗口大小

    Returns:
        修正后的距离序列
    """
    corrected_distances = distances.copy()

    if not outlier_indices:
        return corrected_distances

    # 识别连续的异常串
    outlier_segments = _group_consecutive_indices(outlier_indices)
    print(f"识别到 {len(outlier_segments)} 个异常串需要修正")

    # 分层处理：按误差大小排序异常串
    ranked_segments = _rank_segments_by_error(outlier_segments, distances)
    # print(f"异常串按误差排序: {[(seg['segment'], f'{seg['error']:.1f}cm') for seg in ranked_segments]}")

    # 分层修正：先剔除误差最大的串，再逐步修正
    remaining_outliers = set(outlier_indices)  # 剩余需要修正的异常点

    for layer_idx, segment_info in enumerate(ranked_segments):
        start_idx, end_idx = segment_info['segment']
        error = segment_info['error']

        print(f"\n第{layer_idx+1}层修正: 异常串 [{start_idx}:{end_idx}], 误差={error:.1f}cm")

        # 寻找前后正常串的参考距离（排除剩余的异常点）
        before_distances = []
        after_distances = []

        # 寻找前面的正常点（排除剩余异常点，使用已修正的距离）
        search_start = max(0, start_idx - smoothing_window * 2)
        for i in range(search_start, start_idx):
            if i not in remaining_outliers:  # 排除剩余异常点
                before_distances.append(corrected_distances[i])  # 使用已修正的距离

        # 寻找后面的正常点（排除剩余异常点）
        search_end = min(len(distances), end_idx + 1 + smoothing_window * 2)
        for i in range(end_idx + 1, search_end):
            if i not in remaining_outliers:
                after_distances.append(corrected_distances[i])  # 使用已修正的距离

        # 验证参考距离的有效性
        before_ref = None
        after_ref = None

        if len(before_distances) >= 2:
            before_ref = np.median(before_distances)
            before_std = np.std(before_distances)
            if before_std > smoothing_window:
                print(f"    警告: 前面参考点变化过大(std={before_std:.1f})")
                before_ref = np.percentile(before_distances, 25)

        if len(after_distances) >= 2:
            after_ref = np.median(after_distances)
            after_std = np.std(after_distances)
            if after_std > smoothing_window:
                print(f"    警告: 后面参考点变化过大(std={after_std:.1f})")
                after_ref = np.percentile(after_distances, 25)

        print(f"  前串参考距离: {before_ref:.1f}cm" if before_ref else "  前串参考距离: None")
        print(f"  后串参考距离: {after_ref:.1f}cm" if after_ref else "  后串参考距离: None")

        # 智能选择修正策略
        correction_strategy = _select_correction_strategy(
            before_ref, after_ref, before_distances, after_distances,
            start_idx, end_idx, corrected_distances, list(remaining_outliers)
        )

        print(f"  选择修正策略: {correction_strategy['method']}")
        ref_str = f"{correction_strategy['reference']:.1f}" if correction_strategy['reference'] else "None"
        print(f"  参考值: {ref_str}")

        # 根据策略进行修正
        if correction_strategy['method'] == 'interpolation':
            segment_length = end_idx - start_idx + 1
            for i, idx in enumerate(range(start_idx, end_idx + 1)):
                ratio = i / (segment_length - 1) if segment_length > 1 else 0
                interpolated_dist = before_ref + (after_ref - before_ref) * ratio
                corrected_distances[idx] = interpolated_dist
                print(f"    索引 {idx}: {distances[idx]:.1f} -> {interpolated_dist:.1f}cm (线性插值)")

        elif correction_strategy['method'] == 'single_reference':
            ref_value = correction_strategy['reference']
            for idx in range(start_idx, end_idx + 1):
                corrected_distances[idx] = ref_value
                print(f"    索引 {idx}: {distances[idx]:.1f} -> {ref_value:.1f}cm ({correction_strategy['source']})")

        elif correction_strategy['method'] == 'global_baseline':
            ref_value = correction_strategy['reference']
            for idx in range(start_idx, end_idx + 1):
                corrected_distances[idx] = ref_value
                print(f"    索引 {idx}: {distances[idx]:.1f} -> {ref_value:.1f}cm (全局基线)")
        else:
            print(f"    警告: 无法找到可靠的参考距离，保持原值")

        # 从剩余异常点中移除已修正的点
        for idx in range(start_idx, end_idx + 1):
            remaining_outliers.discard(idx)

    return corrected_distances

def _group_consecutive_indices(indices: List[int]) -> List[Tuple[int, int]]:
    """
    将连续的索引分组为段

    Args:
        indices: 索引列表

    Returns:
        连续段的列表，每个元素为 (start_index, end_index)
    """
    if not indices:
        return []

    segments = []
    start = indices[0]
    end = indices[0]

    for i in range(1, len(indices)):
        if indices[i] == end + 1:
            # 连续的索引，扩展当前段
            end = indices[i]
        else:
            # 不连续，结束当前段，开始新段
            segments.append((start, end))
            start = indices[i]
            end = indices[i]

    # 添加最后一个段
    segments.append((start, end))

    return segments

def _rank_segments_by_error(segments: List[Tuple[int, int]], distances: List[float]) -> List[dict]:
    """
    按误差大小对异常串进行排序

    Args:
        segments: 异常串列表 [(start, end), ...]
        distances: 距离序列

    Returns:
        按误差排序的异常串信息列表
    """
    segment_info = []

    # 计算全局基线（所有点的中位数）
    global_baseline = np.median(distances)

    for start_idx, end_idx in segments:
        segment_distances = distances[start_idx:end_idx+1]
        segment_avg = np.mean(segment_distances)

        # 计算误差：与全局基线的偏差
        error = abs(segment_avg - global_baseline)

        segment_info.append({
            'segment': (start_idx, end_idx),
            'avg_distance': segment_avg,
            'error': error,
            'length': end_idx - start_idx + 1
        })

    # 按误差从大到小排序
    segment_info.sort(key=lambda x: x['error'], reverse=True)

    return segment_info

def _select_correction_strategy(before_ref: float, after_ref: float,
                               before_distances: List[float], after_distances: List[float],
                               start_idx: int, end_idx: int, distances: List[float],
                               outlier_indices: List[int]) -> dict:
    """
    智能选择修正策略，确保不使用异常串的值进行修正

    Returns:
        dict: 包含修正策略信息的字典
    """

    # 策略1: 前后都有可靠参考，且参考值相近
    if before_ref is not None and after_ref is not None:
        ref_diff = abs(before_ref - after_ref)
        if ref_diff <= 3.0:  # 前后参考值相近，可以插值
            return {
                'method': 'interpolation',
                'reference': (before_ref + after_ref) / 2,
                'source': '前后插值'
            }
        else:
            # 前后参考值差异较大，选择更可靠的一个
            if len(before_distances) >= len(after_distances):
                return {
                    'method': 'single_reference',
                    'reference': before_ref,
                    'source': '前串(更可靠)'
                }
            else:
                return {
                    'method': 'single_reference',
                    'reference': after_ref,
                    'source': '后串(更可靠)'
                }

    # 策略2: 只有前面参考
    elif before_ref is not None:
        return {
            'method': 'single_reference',
            'reference': before_ref,
            'source': '前串'
        }

    # 策略3: 只有后面参考
    elif after_ref is not None:
        return {
            'method': 'single_reference',
            'reference': after_ref,
            'source': '后串'
        }

    # 策略4: 没有局部参考，使用全局基线
    else:
        # 计算全局正常点的基线
        valid_distances = [d for i, d in enumerate(distances) if i not in outlier_indices]

        if len(valid_distances) >= 5:
            # 使用全局正常点的稳健统计量
            global_median = np.median(valid_distances)
            global_q25 = np.percentile(valid_distances, 25)
            global_q75 = np.percentile(valid_distances, 75)

            # 如果数据分布相对集中，使用中位数
            if global_q75 - global_q25 <= 10.0:
                return {
                    'method': 'global_baseline',
                    'reference': global_median,
                    'source': '全局中位数'
                }
            else:
                # 数据分布较散，使用更保守的值
                return {
                    'method': 'global_baseline',
                    'reference': global_q25,
                    'source': '全局25%分位数'
                }
        else:
            # 正常点太少，无法修正
            return {
                'method': 'keep_original',
                'reference': None,
                'source': '无可靠参考'
            }

def main():
    args = parse_args()
    # Create camera capture
    cap = cv2.VideoCapture(args.camera_id)
    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
    desired_fps = 30
    cap.set(cv2.CAP_PROP_FPS, desired_fps)
# cap.set(cv2.CAP_PROP_FRAME_WIDTH, 1280)
# cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 960)
    # Set camera resolution
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.frame_size[0])
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.frame_size[1])
    
    # Create output window
    cv2.namedWindow("监控视角 - 检测结果", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("监控视角 - 检测结果", 1280, 720)

    calibration_mapx = "pixel_to_physical_py/config/mapx"
    calibration_mapy = "pixel_to_physical_py/config/mapy"
    physical_processor = _initialize_physical_processor()

    # Load models
    print("正在加载模型...")
    print(os.path.exists(args.checkpoint))
    model = load_model(args.checkpoint, args.device)
    line_model = load_model(args.line_checkpoint, args.device)
    
    # Build preprocessing pipelines
    print("正在构建预处理流程...")
    main_pp = build_preprocessing_pipeline()
    line_pp = build_preprocessing_pipeline()
    
    # Get configuration
    # test_cfg = main_pp['cfg'].model_test_cfg
    test_cfg = EasyDict(
        max_per_img=300,
        multi_label=True,
        nms=dict(iou_threshold=0.7, type='nms'),
        nms_pre=30000,
        score_thr=0.001)
    
    # FPS calculation
    frame_count = 0
    start_time = time.time()
    
    print("开始实时检测，按ESC键退出...")
    while cap.isOpened():
        # Capture frame-by-frame
        ret, frame = cap.read()
        if not ret:
            print("无法获取帧，退出...")
            # continue
            break
        # Convert BGR to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        original_shape = rgb_frame.shape
        
        rgb_frame = _preprocess_image(rgb_frame, calibration_mapx, calibration_mapy)
        # 在去畸变图像上绘制范围框
        x_min, x_max = (0, 1280)
        y_min, y_max = (420, 700)
        
        # 绘制矩形框 (红色，线宽2)
        cv2.rectangle(rgb_frame, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)

        # Main detection processing
        main_data, main_samples = preprocess_image(
            rgb_frame, 
            main_pp['pipeline'], 
            main_pp['preprocessor'], 
            args.device
        )
        main_result = model(main_data)
        main_results = process_detection_results(
            main_result,
            args.device,
            test_cfg,
            original_shape,
            main_samples.get('scale', 1),
            main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
        )
        
        # Line detection processing
        scale_factor = main_samples.get('scale', 1)
        pad_param = main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
        # Line detection processing
        line_data, line_samples = preprocess_image(
            rgb_frame, 
            main_pp['pipeline'], 
            main_pp['preprocessor'], 
            args.device
        )
        line_result = line_model(line_data)
        line_preds, line_scores = torch.split(line_result[0], [2, 1], dim=1)
        _, line_points, _ = get_single_pred(line_scores, line_preds, (640, 640))
        
        # Visualization - convert back to BGR for display
        # vis_img = visualize_detections(rgb_frame, main_results[0][0], class_names=CLASS_NAMES)
        


        # 处理线检测点并转换坐标
        transformed_points = []
        for point in line_points:
            x, y, conf = point
            if conf > 0.5:
                # 坐标转换
                x_actual, y_actual, conf = transform_point(point, scale_factor, pad_param)
                transformed_points.append((x_actual, y_actual, conf))
                # # 在原始图像上绘制圆
                # cv2.circle(rgb_frame, (int(x_actual), int(y_actual)), 5, (0, 0, 255), -1)
        transformed_points = sort_points_by_line(transformed_points)
        # 处理物理距离计算
        detection_results = []
        distance_table_path = "pixel_to_physical_py/config/distance_table"  # 添加距离表路径

        if physical_processor is not None and len(transformed_points) > 0:
            try:
                # 将点转换为DetectionResult格式
                detection_results = _convert_points_to_detection_results(transformed_points)
                print(f"\n检测到 {len(detection_results)} 个目标点")

                # 计算所有点的物理距离
                valid_results = []
                for i, det_result in enumerate(detection_results):
                    print(f"\n--- 处理目标点 {i+1}: {det_result.label} ---")
                    # 使用physical_processor处理检测结果
                    physical_processor.process_detection_result(det_result, distance_table_path)

                    # 只保留成功计算出距离的点
                    if det_result.physical_distance > 0:
                        valid_results.append((det_result, transformed_points[i]))

                # 处理wire变形导致的距离异常
                if len(valid_results) > 0:
                    print(f"\n=== 开始处理距离异常值 (模式: {args.outlier_mode}) ===")
                    processed_results = process_wire_distance_outliers(
                        valid_results,
                        outlier_mode=args.outlier_mode,
                        max_distance_jump=1.0,  # 可根据实际情况调整
                        smoothing_window=3
                    )
                    print(f"处理完成: {len(processed_results)}/{len(valid_results)} 个点保留")
                else:
                    processed_results = valid_results

                # 显示处理后的结果
                print(f"\n=== 可视化 {len(processed_results)} 个处理后的点 ===")
                # for det_result, point_coords in processed_results:
                for det_result, point_coords in processed_results:
                    print(f"目标点 {det_result.label}:")
                    print(f"  物理距离: {det_result.physical_distance:.2f} cm")

                    # 检查是否为修正过的点
                    is_corrected = "[距离已修正]" in det_result.des
                    if is_corrected:
                        print(f"  ✓ 此点已被修正: {det_result.des}")

                    # 在圆圈内显示距离值
                    point_x, point_y = int(point_coords[0]), int(point_coords[1])
                    distance_value = int(det_result.physical_distance)  # 取整数，更简洁
                    distance_text = f"{distance_value}"

                    # 根据距离和是否修正选择颜色
                    if is_corrected:
                        # 修正过的点用特殊颜色标识
                        circle_color = (255, 0, 255)  # 紫色 - 修正过的点
                        border_color = (255, 255, 0)  # 黄色边框
                        print(f"  绘制修正点: 位置({point_x}, {point_y}), 距离={distance_value}cm, 颜色=紫色")
                    else:
                        # 正常点按距离选择颜色
                        if det_result.physical_distance < 30:
                            circle_color = (0, 255, 0)  # 绿色 - 近距离
                        elif det_result.physical_distance < 60:
                            circle_color = (255, 255, 0)  # 黄色 - 中距离
                        else:
                            circle_color = (255, 0, 0)  # 红色 - 远距离
                        border_color = (255, 255, 255)  # 白色边框
                        print(f"  绘制正常点: 位置({point_x}, {point_y}), 距离={distance_value}cm")

                    # 圆圈大小和字体设置
                    circle_radius = 8
                    font_scale = 0.3

                    # 绘制填充的圆圈作为背景
                    cv2.circle(rgb_frame, (point_x, point_y), circle_radius, circle_color, -1)

                    # 绘制圆圈边框（修正过的点用不同颜色边框）
                    cv2.circle(rgb_frame, (point_x, point_y), circle_radius, border_color, 2)

                    # 计算文字位置（居中）
                    text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
                    text_x = point_x - text_size[0] // 2
                    text_y = point_y + text_size[1] // 2

                    # 在圆圈内绘制距离数字（黑色）
                    cv2.putText(rgb_frame, distance_text, (text_x, text_y),
                               cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1)
            except Exception as e:
                print(f"物理距离计算出错: {e}")
                import traceback
                traceback.print_exc()
        

        # Convert back to BGR for OpenCV display
        display_img = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite("output/"+str(frame_count)+".png",display_img)
        # Calculate and display FPS
        frame_count += 1
        if args.fps_display and frame_count % 10 == 0:
            fps = frame_count / (time.time() - start_time)
            cv2.putText(display_img, f"FPS: {fps:.2f}", (20, 50), 
                        cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        # Display the resulting frame
        cv2.imshow("监控视角 - 检测结果", display_img)
        # print(scale_factor,pad_param)

        # Exit on ESC key
        key = cv2.waitKey(1)
        if key == 27:  # ESC key
            print("ESC键按下，退出程序...")
            break
    
    # Cleanup
    cap.release()
    cv2.destroyAllWindows()
    print("程序已结束")

if __name__ == '__main__':
    main()