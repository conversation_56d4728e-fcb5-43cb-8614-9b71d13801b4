# Copyright (c) OpenMMLab. All rights reserved.
import os
import sys
import random
import time
from pathlib import Path
from argparse import ArgumentParser

import cv2
import mmcv
import numpy as np
import torch
from PIL import Image
from typing import List, Tuple

# Custom module imports
sys.path.append(str(Path(__file__).resolve().parents[3]))
sys.path.append('/home/<USER>/panpan/mm_detection/test_carm')

from projects.easydeploy.model import ORTWrapper  # noqa: E402
from utils import (  # noqa: E402
    bbox_postprocess, 
    preprocess, 
    visualize_detections,
    reg_max2bbox,
    resize_and_pad
)
from decode import (  # noqa: E402
    predict_by_feat,
    _bbox_post_process,
    get_single_pred,
    
)
from easydict import EasyDict
from pixel_to_physical_py.src.detection_processor import DetectionProcessor, DetectionResult, BBox
from pixel_to_physical_py.src.camera_coordinate_converter import CameraCoordinateConverter, CameraCoordinate

# Configuration and Constants
COLORS = [[random.randint(0, 255) for _ in range(3)] for _ in range(1000)]
CLASS_NAMES = ['bin', 'cloth', 'rug', 'shoe', 'wire', 'rail', 'wheel']
def transform_point(point, scale_factor, pad_param):
    x, y, conf = point
    top, bottom, left, right = pad_param
    x_actual = (x - left) / scale_factor
    y_actual = (y - top)  / scale_factor
    return (x_actual, y_actual, conf)

def parse_args():
    parser = ArgumentParser()
    parser.add_argument('--camera-id', type=int, default=0)
    parser.add_argument('--frame-size', nargs=2, type=int, default=[1280, 720])
    parser.add_argument('--config', default="yolov8s_old7cls_640.py")
    parser.add_argument('--checkpoint', '-c', default="yolov8v6_only.onnx")
    parser.add_argument('--line_config', default="line_in_five_head.py")
    parser.add_argument('--line_checkpoint', '-l', default="yolov8v13_only.onnx")
    parser.add_argument('--out-dir', default='camera_output')
    parser.add_argument('--device', default='cuda:0')
    parser.add_argument('--fps-display', action='store_true')
    parser.add_argument('--outlier-mode', choices=['filter', 'correct'], default='correct')
    parser.add_argument('--camera-outlier-mode', choices=['filter', 'correct'], default='correct')
    parser.add_argument('--window-size', type=int, default=20)
    parser.add_argument('--threshold-ratio', type=float, default=1)
    parser.add_argument('--prominence', type=float, default=10)
    parser.add_argument('--neighbor-window', type=int, default=2)
    return parser.parse_args()

def load_model(checkpoint_path, device):
    if checkpoint_path.endswith('.onnx'):
        model = ORTWrapper(checkpoint_path, device)
    model.to(device)
    return model

def build_preprocessing_pipeline():
    return {
        'pipeline': resize_and_pad,
        'preprocessor': preprocess(),
    }

def preprocess_image(rgb_img, pipeline, preprocessor, device):
    processed = pipeline(rgb_img)
    data, scale, pad_param = processed
    scale = 1.0/scale
    data = preprocessor(data).to(device)
    samples = {"scale": scale, "pad_param": pad_param}
    return data, samples

def process_detection_results(feats, device, test_cfg, original_shape, scale_factor, pad_param):
    bbox_preds, cls_scores = [], []
    for feat in feats:
        bbox_pred, cls_score = torch.split(feat, [64, 7], dim=1)
        bbox_preds.append(bbox_pred)
        cls_scores.append(cls_score)
    if bbox_preds[0].shape[1] == 64:
        proj = torch.arange(16, dtype=torch.float).to(device)
        bbox_preds = [reg_max2bbox(bbox, proj) for bbox in bbox_preds]
    batch_img_metas = [{
        'ori_shape': original_shape,
        'pad_shape': (640, 640, 3),
        'scale_factor': scale_factor,
        'pad_param': pad_param
    }]
    return predict_by_feat(cls_scores, bbox_preds, objectnesses=None,
                           batch_img_metas=batch_img_metas, cfg=test_cfg,
                           post_processing=_bbox_post_process)

def _preprocess_image(img: np.ndarray, calibration_mapx_path, calibration_mapy_path) -> np.ndarray:
    calibration_mapx = np.fromfile(calibration_mapx_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    calibration_mapy = np.fromfile(calibration_mapy_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    processed_img = cv2.remap(img, calibration_mapx, calibration_mapy, cv2.INTER_LINEAR)
    print("使用标定映射表处理图像")
    return processed_img

def _initialize_physical_processor() -> DetectionProcessor:
    return DetectionProcessor()

def _initialize_camera_converter() -> CameraCoordinateConverter:
    camera_config_path = "pixel_to_physical_py/config/calib_intrix_new.yaml"
    return CameraCoordinateConverter(camera_config_path)

def _convert_points_to_detection_results(points_data: List[Tuple], point_size: int = 10) -> List[DetectionResult]:
    detection_results = []
    for i, (x_actual, y_actual, conf) in enumerate(points_data):
        bbox_obj = BBox(x1=int(x_actual), y1=int(y_actual), x2=int(x_actual), y2=int(y_actual))
        det_result = DetectionResult(bbox=bbox_obj, label=f"point_{i}", confidence=float(conf))
        detection_results.append(det_result)
    return detection_results

from sklearn.ensemble import IsolationForest
def detect_bulges_isolation_forest(distances):
    X = np.array(distances).reshape(-1, 1)
    clf = IsolationForest(contamination="auto", random_state=40)
    outliers = clf.fit_predict(X)
    bulge_indices = np.where(outliers == -1)[0]
    return [(i, distances[i]) for i in bulge_indices]


def find_outlier_indices(distance_list):
    """
    基于滑动窗口和峰值检测方法找出异常点的索引

    参数:
        distance_list (list of str or float): 原始距离序列
        window_size (int): 滑动窗口大小（默认 10）
        threshold_ratio (float): 阈值比例（默认 1.3）
        prominence (float): 峰值突出度（默认 10）

    返回:
        outlier_indices (list of int): 异常值的索引位置
    """
    data = [float(x) for x in distance_list]
    bulges = detect_bulges_isolation_forest(data)
    return bulges

def process_wire_distance_outliers_bulge(camera_coord, valid_results: List[Tuple], outlier_mode: str = 'correct') -> List[Tuple]:
    """
    使用滑动窗口和峰值检测方法处理wire检测点的距离异常值（隆起部分）

    Args:
        valid_results: 包含(DetectionResult, point_coords)的列表
        outlier_mode: 处理模式 'filter'=删除异常点, 'correct'=修正异常点距离
        window_size: 滑动窗口大小（默认 10）
        threshold_ratio: 阈值比例（默认 1.3）
        prominence: 峰值突出度（默认 10）
        neighbor_window: 校正时使用的邻居窗口大小（默认 2）

    Returns:
        处理后的结果列表
    """
    if len(valid_results) < 3:
        print("检测点数量不足，跳过异常值处理")
        return valid_results

    # 按照x坐标从左到右排序
    sorted_results = sorted(valid_results, key=lambda x: x[1][0])  # x[1][0] 是 point_coords 的 x 坐标

    distances = [result[0].physical_distance for result in sorted_results]
    print(f"原始距离序列: {[f'{d:.1f}' for d in distances]}")
    print(f"相机坐标y序列: {[f'{coord.y:.1f}' for coord in camera_coord]}")

    # 使用滑动窗口和峰值检测方法检测异常点（隆起部分）
    # outlier_indices = find_outlier_indices(distances)
    # d = [v for _, v in outlier_indices]
    # outlier_indices = find_3d_outlier_jumps(distance)
    # anomaly_points = detect_anomaly_points(distances, contamination=0.1)
    # anomaly_sequences = detect_abnormal_sequences(distances, diff_threshold=8, min_len=2)
    # outlier_indices = sorted(set(list(anomaly_points)) | set(list(anomaly_sequences)))

    outlier_indices, _ = detect_large_distance_errors(distances, threshold=10)
    # _,outlier_indices = filter_bulge_points(camera_coord)

    # combined_abnormal_values = [distances[i] for i in combined_abnormal_indices]
    
    if not outlier_indices:
        print("未检测到距离异常点（隆起部分）")
        return sorted_results

    # print(f"滑动窗口+峰值检测到异常点索引: {outlier_indices}")
    # print(f"异常点距离值: {[f'{distances[i]:.1f}' for i in outlier_indices]}")
    # 只提取索引部分
    # outlier_indices = [idx for (idx, val) in outlier_indices]
    # print(f"异常点距离值: {[f'{distances[i]:.1f}' for i in outlier_indices]}")

    if outlier_mode == 'filter':
        # 模式1: 删除异常点
        filtered_results = []
        for i, result in enumerate(sorted_results):
            if i not in outlier_indices:
                filtered_results.append(result)
            else:
                print(f"删除异常点 {i}: 距离={result[0].physical_distance:.1f}cm")

        print(f"过滤后保留 {len(filtered_results)}/{len(sorted_results)} 个点")
        return filtered_results

    return sorted_results


def compute_3d_distances(valid_results, camera_converter):
    camera_coords = []
    for det_result, point_coords in valid_results:
        d = det_result.physical_distance
        if d <= 0:
            camera_coords.append(None)
        else:
            cam_point = camera_converter.pixel_to_camera_coordinate(point_coords[0], point_coords[1], d)
            camera_coords.append(cam_point)

    # distances = []
    # for i in range(1, len(camera_coords)):
    #     p1 = camera_coords[i - 1]
    #     p2 = camera_coords[i]
    #     if p1 is None or p2 is None:
    #         distances.append(None)
    #     else:
    #         dx = p1.x - p2.x
    #         dy = p1.y - p2.y
    #         dz = p1.z - p2.z
    #         dist = np.sqrt(dx**2 + dy**2 + dz**2)
    #         distances.append(dist)
    # return distances
    return camera_coords


def find_3d_outlier_jumps(distances, threshold=5.0):
    """
    距离突变超过 threshold 的点认为是异常点
    """
    outlier_indices = []
    for i, d in enumerate(distances):
        if d is not None and d > threshold:
            outlier_indices.append(i)  # 表示 i 与 i+1 之间跳变
    return outlier_indices

def detect_abnormal_sequences(values, diff_threshold=8, min_len=2):
    diffs = np.diff(values)
    abnormal_indices = np.where(np.abs(diffs) > diff_threshold)[0]

    abnormal_set = set()
    current = []
    for idx in abnormal_indices:
        if not current or idx == current[-1] + 1:
            current.append(idx)
        else:
            if len(current) >= min_len:
                for i in current:
                    abnormal_set.update([i, i + 1])
            current = [idx]
    if len(current) >= min_len:
        for i in current:
            abnormal_set.update([i, i + 1])
    return abnormal_set  # set[int]

def is_bulge_by_y(z_mean, y_val):
    """
    根据 z 均值所在区间，判断相机坐标 y 是否超过隆起阈值
    """
    if z_mean <= 20:          # 0–20 cm
        return y_val > 8.1
    elif z_mean <= 30:        # 20–30 cm
        return y_val > 8.3
    elif z_mean <= 40:        # 30–40 cm
        return y_val > 8.7
    elif z_mean <= 50:        # 40–50 cm
        return y_val > 8.9
    else:                       # >50 cm，可按需拓展
        return y_val > 9.3

import numpy as np
from typing import List
def filter_bulge_points(coords: List[CameraCoordinate]):
    # 提取 z 值用于计算均值（剔除 z<=0 的点）
    z_vals = np.array([p.z for p in coords if p.z > 0])
    if len(z_vals) == 0:
        return [], list(range(len(coords)))  # 全部无效
    
    # z_mean = z_vals.mean()
    z_mean = min(z_vals)
    print(f"z_min: {z_mean}")
    bulge_indices = []
    kept_indices = []

    for idx, p in enumerate(coords):
        if p.z <= 0:
            continue
        if is_bulge_by_y(z_mean, p.y):
            bulge_indices.append(idx)
        else:
            kept_indices.append(idx)

    return kept_indices, bulge_indices



from sklearn.ensemble import IsolationForest
import numpy as np

def detect_anomaly_points(values, contamination=0.1):
    X = np.array(values).reshape(-1, 1)
    clf = IsolationForest(contamination=contamination, random_state=42)
    preds = clf.fit_predict(X)
    return set(np.where(preds == -1)[0].tolist())  # 保证是 set[int]

def detect_large_distance_errors(distances_str, threshold=5):
    """
    基于“从最小距离开始，小的是正常，大的是误差”规则，
    检测距离列表中异常的大距离点。

    参数：
    - distances_str: list[str]，距离值字符串列表
    - threshold: float，超过最小值多少算异常，默认10

    返回：
    - abnormal_indices: list[int]，异常点索引
    - abnormal_values: list[float]，异常点对应距离值
    """
    distances = list(map(float, distances_str))
    # min_val = min(distances)
    median_val = np.median(distances)
    # 找第一个最小值的索引（如果有多个最小值）
    # min_index = distances.index(median_val)

    # print(f"最小距离: {median_val:.1f}cm, 索引: {min_index}")

    abnormal_indices = [i for i, v in enumerate(distances) if v > median_val + threshold]
    abnormal_values = [distances[i] for i in abnormal_indices]

    for i, v in enumerate(distances):
        if v > 45:
            abnormal_indices.append(i)
            abnormal_values.append(v)
    return abnormal_indices, abnormal_values
import math
def sort_points_by_line(points):
    """
    按线条方向重新排序像素坐标点
    :param points: 包含(x,y)坐标的列表，格式可以是元组或列表
    :return: 按线条方向排序后的坐标列表
    """
    if not points:
        return []
    
    # 转换为(x,y)元组列表（如果输入是其他格式）
    coords = [(float(p[0]), float(p[1]), float(p[2])) if isinstance(p, (list, tuple)) else p for p in points]
    
    # 选择最下方的点作为起始点（y最大）
    start_point = max(coords, key=lambda p: (p[0], p[1]))
    sorted_points = [start_point]
    remaining_points = coords.copy()
    remaining_points.remove(start_point)
    
    while remaining_points:
        last_point = sorted_points[-1]
        # 找到离last_point最近的点
        nearest_point = min(remaining_points, 
                          key=lambda p: math.sqrt((p[0]-last_point[0])**2 + (p[1]-last_point[1])**2))
        sorted_points.append(nearest_point)
        remaining_points.remove(nearest_point)
    
    return sorted_points


def main():
    args = parse_args()
    cap = cv2.VideoCapture(args.camera_id)
    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
    cap.set(cv2.CAP_PROP_FPS, 30)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.frame_size[0])
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.frame_size[1])
    cv2.namedWindow("监控视角 - 检测结果", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("监控视角 - 检测结果", 1280, 720)

    calibration_mapx = "pixel_to_physical_py/config/mapx"
    calibration_mapy = "pixel_to_physical_py/config/mapy"
    physical_processor = _initialize_physical_processor()
    camera_converter = _initialize_camera_converter()

    model = load_model(args.checkpoint, args.device)
    line_model = load_model(args.line_checkpoint, args.device)
    main_pp = build_preprocessing_pipeline()
    test_cfg = EasyDict(
        max_per_img=300,
        multi_label=True,
        nms=dict(iou_threshold=0.7, type='nms'),
        nms_pre=30000,
        score_thr=0.001)

    frame_count, start_time = 0, time.time()
    print("开始实时检测，按ESC键退出...")

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        original_shape = rgb_frame.shape
        rgb_frame = _preprocess_image(rgb_frame, calibration_mapx, calibration_mapy)
        
        

        main_data, main_samples = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
        main_result = model(main_data)
        main_results = process_detection_results(main_result, args.device, test_cfg, original_shape,
                                                 main_samples.get('scale', 1), main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32)))

        line_data, _ = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
        line_result = line_model(line_data)
        line_preds, line_scores = torch.split(line_result[0], [2, 1], dim=1)
        _, line_points, _ = get_single_pred(line_scores, line_preds, (640, 640))

        scale_factor = main_samples.get('scale', 1)
        pad_param = main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
        transformed_points = [transform_point(p, scale_factor, pad_param) for p in line_points if p[2] > 0.5]

        # # 先绘制原始检测点（红色小圆点）
        # for pt in transformed_points:
        #     cv2.circle(rgb_frame, (int(pt[0]), int(pt[1])), 5, (0, 0, 255), -1)
        transformed_points = sort_points_by_line(transformed_points)
        distance_table_path = "pixel_to_physical_py/config/distance_table"
        detection_results = _convert_points_to_detection_results(transformed_points)
        valid_results = []

        for i, det_result in enumerate(detection_results):
            physical_processor.process_detection_result(det_result, distance_table_path)
            if det_result.physical_distance > 0:
                valid_results.append((det_result, transformed_points[i]))
        

        # processed_results = process_wire_distance_outliers_bulge(
        #         valid_results,
        #         outlier_mode=args.outlier_mode
        #     )

        camera_coord = compute_3d_distances(valid_results, camera_converter)
        processed_results = process_wire_distance_outliers_bulge(
                camera_coord,
                valid_results,
                outlier_mode=args.outlier_mode
            )

        # 可视化距离值
        
        print(f"\n=== 可视化 {len(processed_results)} 个检测点的距离值 ===")
        for i, (det_result, point_coords) in enumerate(processed_results):
            point_x, point_y = int(point_coords[0]), int(point_coords[1])
            distance_value = det_result.physical_distance

            # camera_coord = camera_converter.pixel_to_camera_coordinate(point_x, point_y, distance_value)
            # print(f"  点 {i+1}: 位置({point_x}, {point_y}), 距离={distance_value:.1f}cm, 相机坐标=({camera_coord.x:.1f}, {camera_coord.y:.1f}, {camera_coord.z:.1f})")

            # filter_bulges_by_y(camera_coord, outlier_trim=True)

            # 在圆圈内显示距离值
            point_x, point_y = int(point_coords[0]), int(point_coords[1])
            distance_value = int(det_result.physical_distance)  # 取整数，更简洁
            distance_text = f"{distance_value}"

            # 根据距离选择颜色
            if distance_value < 30:
                circle_color = (0, 255, 0)  # 绿色 - 近距离
            elif distance_value < 60:
                circle_color = (255, 255, 0)  # 黄色 - 中距离
            else:
                circle_color = (255, 0, 0)  # 红色 - 远距离

            border_color = (255, 255, 255)  # 白色边框

            # 圆圈和字体设置
            circle_radius = 8
            font_scale = 0.3

            

            # 绘制填充的圆圈作为背景
            cv2.circle(rgb_frame, (point_x, point_y), circle_radius, circle_color, -1)

            # 绘制圆圈边框（修正过的点用不同颜色边框）
            cv2.circle(rgb_frame, (point_x, point_y), circle_radius, border_color, 2)

            # 计算文字位置（居中）
            text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
            text_x = point_x - text_size[0] // 2
            text_y = point_y + text_size[1] // 2

            # 在圆圈内绘制距离数字（黑色）
            cv2.putText(rgb_frame, distance_text, (text_x, text_y),
                        cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1)

            print(f"  点 {i+1}: 位置({point_x}, {point_y}),距离={distance_value:.1f}cm")
        # 在去畸变图像上绘制范围框
        x_min, x_max = (0, 1280)
        y_min, y_max = (480, 700)
        # 绘制矩形框 (红色，线宽2)
        cv2.rectangle(rgb_frame, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)
        display_img = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite(f"output/{frame_count}.png", display_img)
        frame_count += 1
        if args.fps_display and frame_count % 10 == 0:
            fps = frame_count / (time.time() - start_time)
            cv2.putText(display_img, f"FPS: {fps:.2f}", (20, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        cv2.imshow("监控视角 - 检测结果", display_img)
        if cv2.waitKey(1) == 27:
            break

    cap.release()
    cv2.destroyAllWindows()
    print("程序已结束")

if __name__ == '__main__':
    main()

