# Copyright (c) OpenMMLab. All rights reserved.
import os
import sys
import random
import time
from pathlib import Path
from argparse import ArgumentParser

import cv2
import mmcv
import numpy as np
import torch
from PIL import Image
from typing import List, Tuple

# Custom module imports
sys.path.append(str(Path(__file__).resolve().parents[3]))
sys.path.append('/home/<USER>/panpan/mm_detection/test_carm')

from projects.easydeploy.model import ORTWrapper  # noqa: E402
from utils import (  # noqa: E402
    bbox_postprocess, 
    preprocess, 
    visualize_detections,
    reg_max2bbox,
    resize_and_pad
)
from decode import (  # noqa: E402
    predict_by_feat,
    _bbox_post_process,
    get_single_pred,
    
)
from easydict import EasyDict
from pixel_to_physical_py.src.detection_processor import DetectionProcessor, DetectionResult, BBox
from pixel_to_physical_py.src.camera_coordinate_converter import CameraCoordinateConverter, CameraCoordinate

# Configuration and Constants
COLORS = [[random.randint(0, 255) for _ in range(3)] for _ in range(1000)]
CLASS_NAMES = ['bin', 'cloth', 'rug', 'shoe', 'wire', 'rail', 'wheel']
def transform_point(point, scale_factor, pad_param):
    x, y, conf = point
    top, bottom, left, right = pad_param
    x_actual = (x - left) / scale_factor
    y_actual = (y - top)  / scale_factor
    return (x_actual, y_actual, conf)

def parse_args():
    parser = ArgumentParser()
    parser.add_argument('--camera-id', type=int, default=0)
    parser.add_argument('--frame-size', nargs=2, type=int, default=[1280, 720])
    parser.add_argument('--config', default="yolov8s_old7cls_640.py")
    parser.add_argument('--checkpoint', '-c', default="yolov8v6_only.onnx")
    parser.add_argument('--line_config', default="line_in_five_head.py")
    parser.add_argument('--line_checkpoint', '-l', default="yolov8v13_only.onnx")
    parser.add_argument('--out-dir', default='camera_output')
    parser.add_argument('--device', default='cuda:0')
    parser.add_argument('--fps-display', action='store_true')
    parser.add_argument('--outlier-mode', choices=['filter', 'correct'], default='filter')
    parser.add_argument('--camera-outlier-mode', choices=['filter', 'correct'], default='correct')
    parser.add_argument('--window-size', type=int, default=20)
    parser.add_argument('--threshold-ratio', type=float, default=1)
    parser.add_argument('--prominence', type=float, default=10)
    parser.add_argument('--neighbor-window', type=int, default=2)
    return parser.parse_args()

def load_model(checkpoint_path, device):
    if checkpoint_path.endswith('.onnx'):
        model = ORTWrapper(checkpoint_path, device)
    model.to(device)
    return model

def build_preprocessing_pipeline():
    return {
        'pipeline': resize_and_pad,
        'preprocessor': preprocess(),
    }

def preprocess_image(rgb_img, pipeline, preprocessor, device):
    processed = pipeline(rgb_img)
    data, scale, pad_param = processed
    scale = 1.0/scale
    data = preprocessor(data).to(device)
    samples = {"scale": scale, "pad_param": pad_param}
    return data, samples

def process_detection_results(feats, device, test_cfg, original_shape, scale_factor, pad_param):
    bbox_preds, cls_scores = [], []
    for feat in feats:
        bbox_pred, cls_score = torch.split(feat, [64, 7], dim=1)
        bbox_preds.append(bbox_pred)
        cls_scores.append(cls_score)
    if bbox_preds[0].shape[1] == 64:
        proj = torch.arange(16, dtype=torch.float).to(device)
        bbox_preds = [reg_max2bbox(bbox, proj) for bbox in bbox_preds]
    batch_img_metas = [{
        'ori_shape': original_shape,
        'pad_shape': (640, 640, 3),
        'scale_factor': scale_factor,
        'pad_param': pad_param
    }]
    return predict_by_feat(cls_scores, bbox_preds, objectnesses=None,
                           batch_img_metas=batch_img_metas, cfg=test_cfg,
                           post_processing=_bbox_post_process)

def _preprocess_image(img: np.ndarray, calibration_mapx_path, calibration_mapy_path) -> np.ndarray:
    calibration_mapx = np.fromfile(calibration_mapx_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    calibration_mapy = np.fromfile(calibration_mapy_path, dtype=np.float32).reshape(img.shape[0], img.shape[1])
    processed_img = cv2.remap(img, calibration_mapx, calibration_mapy, cv2.INTER_LINEAR)
    print("使用标定映射表处理图像")
    return processed_img

def _initialize_physical_processor() -> DetectionProcessor:
    return DetectionProcessor()

def _initialize_camera_converter() -> CameraCoordinateConverter:
    camera_config_path = "pixel_to_physical_py/config/calib_intrix_new.yaml"
    return CameraCoordinateConverter(camera_config_path)

def _convert_points_to_detection_results(points_data: List[Tuple], point_size: int = 10) -> List[DetectionResult]:
    detection_results = []
    for i, (x_actual, y_actual, conf) in enumerate(points_data):
        bbox_obj = BBox(x1=int(x_actual), y1=int(y_actual), x2=int(x_actual), y2=int(y_actual))
        det_result = DetectionResult(bbox=bbox_obj, label=f"point_{i}", confidence=float(conf))
        detection_results.append(det_result)
    return detection_results

def find_abnormal_distance_points(distance_sequence):
    """
    找出距离序列中所有突然增大的异常点
    
    参数:
        distance_sequence: 字符串或数字形式的距离序列
        
    返回:
        tuple: (max_jump_start, max_jump_value, abnormal_points)
            - max_jump_start: 最大跳跃的起始点值
            - max_jump_value: 最大跳跃的差值
            - abnormal_points: 所有大于max_jump_start的异常点列表
    """
    # 将字符串序列转换为浮点数
    distances = [float(d) for d in distance_sequence]
    
    # 计算相邻点之间的正向差值
    positive_deltas = []
    for i in range(1, len(distances)):
        delta = distances[i] - distances[i-1]
        if delta > 0:  # 只保留正向差值
            positive_deltas.append((distances[i-1], delta, i))
    
    if not positive_deltas:
        return None, None, []  # 如果没有正向跳跃
    
    # 找到最大的正向跳跃
    max_jump = max(positive_deltas, key=lambda x: x[1])
    max_jump_start, max_jump_value, max_jump_index = max_jump
    
    # 找出所有大于max_jump_start的点
    abnormal_points = [
        (i, distances[i]) 
        for i in range(max_jump_index,len(distances)) 
        if distances[i] > max_jump_start
    ]
    outlier_indices = [i for i, _ in abnormal_points]
    print(f"最大跳跃起始点: {max_jump_start}, 差值: {max_jump_value}")
    print(f"检测到异常点: {abnormal_points}")
    return outlier_indices



import math

def sort_points_by_line(points):
    """
    按线条方向重新排序像素坐标点
    :param points: 包含(x,y)坐标的列表，格式可以是元组或列表
    :return: 按线条方向排序后的坐标列表
    """
    if not points:
        return []
    
    # 转换为(x,y)元组列表（如果输入是其他格式）
    coords = [(float(p[0]), float(p[1]), float(p[2])) if isinstance(p, (list, tuple)) else p for p in points]
    
    # 选择最下方的点作为起始点（y最大）
    start_point = max(coords, key=lambda p: (p[0], p[1]))
    sorted_points = [start_point]
    remaining_points = coords.copy()
    remaining_points.remove(start_point)
    
    while remaining_points:
        last_point = sorted_points[-1]
        # 找到离last_point最近的点
        nearest_point = min(remaining_points, 
                          key=lambda p: math.sqrt((p[0]-last_point[0])**2 + (p[1]-last_point[1])**2))
        sorted_points.append(nearest_point)
        remaining_points.remove(nearest_point)
    
    return sorted_points


def sort_points_with_confidence(points):
    """
    从右下角开始排序，同时保留每个点的额外数据（如 confidence）
    :param points: List[Tuple[float, float, Any]]，例如 (x, y, confidence)
    :return: 排序后的列表，保持 (x, y, confidence) 结构
    """
    if not points:
        return []

    # 去重（基于 (x, y)，保留第一个出现的点）
    unique_points = []
    seen = set()
    for point in points:
        xy = (point[0], point[1])
        if xy not in seen:
            seen.add(xy)
            unique_points.append(point)

    # 选择起始点：右下角（x最大，y最大）
    start_point = max(unique_points, key=lambda p: (p[0], p[1]))
    sorted_points = [start_point]
    remaining_points = unique_points.copy()
    remaining_points.remove(start_point)

    # 最近邻排序（基于 (x, y)）
    while remaining_points:
        last_x, last_y, _ = sorted_points[-1]
        nearest_point = min(
            remaining_points,
            key=lambda p: math.sqrt((p[0] - last_x)**2 + (p[1] - last_y)**2)
        )
        sorted_points.append(nearest_point)
        remaining_points.remove(nearest_point)

    return sorted_points


def _detect_local_outliers(distances: List[float], threshold: float) -> List[int]:
    """
    检测局部异常点（在序列中突然跳高的孤立点或小段）

    Args:
        distances: 距离序列
        threshold: 异常检测阈值

    Returns:
        局部异常点的索引列表
    """
    if len(distances) < 3:
        return []

    outliers = []

    # 方法1: 检测孤立的高峰点
    for i in range(1, len(distances) - 1):
        curr_dist = distances[i]
        prev_dist = distances[i-1]
        next_dist = distances[i+1]

        # 如果当前点比前后点都高很多
        if (curr_dist - prev_dist > threshold and
            curr_dist - next_dist > threshold):
            outliers.append(i)
            print(f"检测到孤立高峰点 {i}: {curr_dist:.1f}cm (前:{prev_dist:.1f}, 后:{next_dist:.1f})")

    # 方法2: 检测连续的高距离段（相对于周围环境）- 更严格的条件
    window_size = 4  # 增大窗口
    for i in range(window_size, len(distances) - window_size):
        # 计算当前点周围的局部环境
        before_window = distances[i-window_size:i]
        after_window = distances[i+1:i+1+window_size]
        local_env = before_window + after_window

        if local_env and len(local_env) >= 6:  # 确保有足够的环境数据
            local_median = np.median(local_env)
            local_std = np.std(local_env)
            curr_dist = distances[i]

            # 更严格的条件：既要比中位数高，也要超过局部标准差
            if (curr_dist - local_median > threshold * 1.2 and
                curr_dist - local_median > local_std * 2):
                if i not in outliers:  # 避免重复添加
                    outliers.append(i)
                    print(f"检测到局部异常点 {i}: {curr_dist:.1f}cm (局部环境中位数:{local_median:.1f}, 标准差:{local_std:.1f})")

    # 方法3: 检测相对于全局基线的异常点 - 仅用于验证
    # 计算移动中位数作为基线
    baseline = []
    window = 7  # 增大窗口
    for i in range(len(distances)):
        start = max(0, i - window//2)
        end = min(len(distances), i + window//2 + 1)
        window_data = distances[start:end]
        baseline.append(np.median(window_data))

    # 只检测明显的异常点
    global_std = np.std(distances)
    for i, (dist, base) in enumerate(zip(distances, baseline)):
        if (dist - base > threshold * 1.8 and  # 更严格的阈值
            dist - base > global_std * 1.5):   # 同时考虑全局标准差
            if i not in outliers:
                outliers.append(i)
                print(f"检测到基线异常点 {i}: {dist:.1f}cm (基线:{base:.1f}, 差异:{dist-base:.1f})")

    return sorted(outliers)

# def find_peaks(distances, window_size=5, threshold_std=2.0):
#     """
#     检测排序后的点序列中的隆起部分（z值异常高的点）
    
#     :param points: List[Tuple[float, float, float]]，格式为 (x, y, z)
#     :param window_size: 滑动窗口大小（用于计算局部均值）
#     :param threshold_std: 判定异常的阈值（标准差的倍数）
#     :return: List[Tuple[int, Tuple[float, float, float]]]，异常点的索引和坐标
#     """
#     z_values = distances
#     n = len(z_values)
#     peaks = []
    
#     for i in range(n):
#         # 计算滑动窗口范围（避免越界）
#         left = max(0, i - window_size)
#         right = min(n, i + window_size + 1)
#         window = z_values[left:right]
        
#         # 计算局部均值和标准差
#         local_mean = np.mean(window)
#         local_std = np.std(window)
        
#         # 动态阈值：均值 + k倍标准差
#         dynamic_threshold = local_mean + threshold_std * local_std
        
#         # 检测异常点
#         if z_values[i] > dynamic_threshold:
#             peaks.append(i)
    
#     return peaks


def detect_anomalous_points(distances, pixel_coords, distance_threshold=2, y_threshold=2, value_threshold=0.5, max_distance_change=3):
    """
    根据距离变化和像素坐标y变化，检测异常点。
    增加了最小差异阈值用于检测相邻值相同的情况，并删除相邻距离差大于 3cm 的点。

    参数：
    distances (list or np.array): 距离序列
    pixel_coords (list of tuples): 像素坐标(x, y)的列表
    distance_threshold (float): 距离变化的阈值
    y_threshold (float): 像素坐标y值变化的阈值
    value_threshold (float): 判断距离值相同或接近的最小差异
    max_distance_change (float): 相邻距离变化最大允许值（单位：cm）

    返回：
    list: 异常点的位置索引
    list: 对应的异常距离值
    list: 对应的异常像素坐标
    """
    
    # 检查 distances 和 pixel_coords 是否为空，或者长度小于 2
    if len(distances) < 2 or len(pixel_coords) < 2:
        print("输入的距离数据或像素坐标数据不足两个点，无法检测异常点。")
        return [], [], []

    # 转换为 numpy 数组
    distances = np.array(distances)
    pixel_coords = np.array(pixel_coords)

    # 提取 y 坐标
    y_coords = np.array([y for x, y in pixel_coords])

    # 计算相邻距离的变化率
    distance_changes = np.diff(distances)

    # # 删除相邻距离变化大于 max_distance_change 的点
    valid_indices = [0]  # 保留第一个点
    for i in range(1, len(distance_changes)):
        if abs(distance_changes[i - 1]) <= max_distance_change:
            valid_indices.append(i)  # 保留当前点

    # 筛选有效的距离和像素坐标
    distances = distances[valid_indices]
    pixel_coords = pixel_coords[valid_indices]
    y_coords = y_coords[valid_indices]

    # 计算相邻y坐标的变化
    y_changes = np.diff(y_coords)

    # 计算变化率的均值和标准差
    mean_distance_change = np.mean(np.diff(distances))
    std_distance_change = np.std(np.diff(distances))

    # 计算y坐标变化的均值和标准差
    mean_y_change = np.mean(y_changes)
    std_y_change = np.std(y_changes)

    # 存放异常点的位置、距离值和像素坐标
    outliers_indices = []
    outliers_distances = []
    outliers_coords = []
    outliers_indices.append(0)
    # 处理第一个点（与第二个点的变化）
    if abs(distances[1] - distances[0]) > distance_threshold * std_distance_change or abs(y_coords[1] - y_coords[0]) > y_threshold * std_y_change:
        outliers_indices.append(0)  # 第一个点
        outliers_distances.append(distances[0])  # 对应的异常距离值
        outliers_coords.append(pixel_coords[0])  # 对应的异常像素坐标
    
    # 判断中间点（相邻点之间的变化）
    for i in range(1, len(distances)):  # 避免 i 超过有效索引
        # print(len(distances))
        # print(i)
        # 如果相邻距离变化小于阈值，视为异常点
        if abs(distances[i] - distances[i - 1]) < value_threshold:
            outliers_indices.append(i)  # 异常点的索引
            outliers_distances.append(distances[i])  # 对应的异常距离值
            outliers_coords.append(pixel_coords[i])  # 对应的异常像素坐标
        elif (abs(np.diff(distances)[i - 1] - mean_distance_change) > distance_threshold * std_distance_change or
              abs(y_changes[i - 1] - mean_y_change) > y_threshold * std_y_change):
            outliers_indices.append(i)  # 异常点的索引
            outliers_distances.append(distances[i])  # 对应的异常距离值
            outliers_coords.append(pixel_coords[i])  # 对应的异常像素坐标

    # 处理最后一个点（与倒数第二个点的变化）
    if abs(distances[-1] - distances[-2]) > distance_threshold * std_distance_change or abs(y_coords[-1] - y_coords[-2]) > y_threshold * std_y_change:
        outliers_indices.append(len(distances) - 1)  # 最后一个点
        outliers_distances.append(distances[-1])  # 对应的异常距离值
        outliers_coords.append(pixel_coords[-1])  # 对应的异常像素坐标
    outliers_indices.append(len(distances) - 1)
    return outliers_indices, outliers_distances, outliers_coords

def find_non_smooth_segments(distance_sequence):
    # 将字符串序列转换为浮点数
    distances = [float(d) for d in distance_sequence]
    
    # 计算一阶差分（梯度）
    first_diff = [distances[i] - distances[i-1] for i in range(1, len(distances))]
    
    # 计算二阶差分（梯度的梯度）
    second_diff = [first_diff[i] - first_diff[i-1] for i in range(1, len(first_diff))]
    
    # 设定二阶差分的阈值（可以根据实际情况调整）
    threshold = 1.0  # 假设二阶差分绝对值超过5.0为不平滑
    
    # 找出不平滑的点（二阶差分绝对值大于阈值）
    non_smooth_indices = [i+1 for i in range(len(second_diff)) if abs(second_diff[i]) > threshold]
    
    # # 将不平滑的点分组为连续的段
    # segments = []
    # if non_smooth_indices:
    #     current_segment = [non_smooth_indices[0]]
    #     for i in range(1, len(non_smooth_indices)):
    #         if non_smooth_indices[i] == non_smooth_indices[i-1] + 1:
    #             current_segment.append(non_smooth_indices[i])
    #         else:
    #             segments.append(current_segment)
    #             current_segment = [non_smooth_indices[i]]
    #     segments.append(current_segment)
    
    # return segments, first_diff, second_diff
    return non_smooth_indices

import numpy as np

def find_abrupt_changes(distances, window_size=5, threshold_std=2.0, direction='both'):
    """
    检测距离序列中的陡然变化（异常增大或减小）
    
    :param distances: List[float]，距离序列
    :param window_size: 滑动窗口大小（用于计算局部统计量）
    :param threshold_std: 判定异常的阈值（标准差的倍数）
    :param direction: 'both'（双向检测）, 'up'（仅检测增大）, 'down'（仅检测减小）
    :return: List[Tuple[int, float]]，异常点的索引和值
    """
    n = len(distances)
    abrupt_changes = []
    
    for i in range(n):
        # 计算滑动窗口范围（避免越界）
        left = max(0, i - window_size)
        right = min(n, i + window_size + 1)
        window = distances[left:right]
        
        # 计算局部均值、标准差和动态阈值
        local_mean = np.mean(window)
        local_std = np.std(window)
        
        # 根据方向设定检测条件
        if direction == 'up':
            dynamic_threshold = local_mean + threshold_std * local_std
            if distances[i] > dynamic_threshold:
                abrupt_changes.append((i, distances[i]))
        elif direction == 'down':
            dynamic_threshold = local_mean - threshold_std * local_std
            if distances[i] < dynamic_threshold:
                abrupt_changes.append((i, distances[i]))
        else:  # 'both'
            upper_threshold = local_mean + threshold_std * local_std
            lower_threshold = local_mean - threshold_std * local_std
            if distances[i] > upper_threshold or distances[i] < lower_threshold:
                # abrupt_changes.append((i, distances[i]))
                abrupt_changes.append(i)
    
    return abrupt_changes

# def find_abrupt_changes(distances, window_size=5, threshold_std=2.0):
#     n = len(distances)
#     abrupt_up = []
#     abrupt_down = []
    
#     for i in range(1, n):
#         left = max(0, i - window_size)
#         right = min(n, i + window_size + 1)
#         window = distances[left:right]
        
#         local_mean = np.mean(window)
#         local_std = np.std(window)
#         diff = distances[i] - distances[i-1]
        
#         if diff > local_mean + threshold_std * local_std:
#             abrupt_up.append(i)
#         elif diff < local_mean - threshold_std * local_std:
#             abrupt_up.append(i)
    
#     return abrupt_up



def main():
    args = parse_args()
    cap = cv2.VideoCapture(args.camera_id)
    cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
    cap.set(cv2.CAP_PROP_FPS, 30)
    cap.set(cv2.CAP_PROP_FRAME_WIDTH, args.frame_size[0])
    cap.set(cv2.CAP_PROP_FRAME_HEIGHT, args.frame_size[1])
    cv2.namedWindow("监控视角 - 检测结果", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("监控视角 - 检测结果", 1280, 720)

    calibration_mapx = "pixel_to_physical_py/config/mapx"
    calibration_mapy = "pixel_to_physical_py/config/mapy"
    physical_processor = _initialize_physical_processor()
    camera_converter = _initialize_camera_converter()

    model = load_model(args.checkpoint, args.device)
    line_model = load_model(args.line_checkpoint, args.device)
    main_pp = build_preprocessing_pipeline()
    test_cfg = EasyDict(
        max_per_img=300,
        multi_label=True,
        nms=dict(iou_threshold=0.7, type='nms'),
        nms_pre=30000,
        score_thr=0.001)

    frame_count, start_time = 0, time.time()
    print("开始实时检测，按ESC键退出...")

    while cap.isOpened():
        ret, frame = cap.read()
        if not ret:
            break
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        original_shape = rgb_frame.shape
        rgb_frame = _preprocess_image(rgb_frame, calibration_mapx, calibration_mapy)
        
        

        main_data, main_samples = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
        main_result = model(main_data)
        main_results = process_detection_results(main_result, args.device, test_cfg, original_shape,
                                                 main_samples.get('scale', 1), main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32)))

        line_data, _ = preprocess_image(rgb_frame, main_pp['pipeline'], main_pp['preprocessor'], args.device)
        line_result = line_model(line_data)
        line_preds, line_scores = torch.split(line_result[0], [2, 1], dim=1)
        _, line_points, _ = get_single_pred(line_scores, line_preds, (640, 640))

        scale_factor = main_samples.get('scale', 1)
        pad_param = main_samples.get('pad_param', np.array([0, 0, 0, 0], dtype=np.float32))
        transformed_points = [transform_point(p, scale_factor, pad_param) for p in line_points if p[2] > 0.5]

        # 先绘制原始检测点（红色小圆点）
        # for pt in transformed_points:
        #     cv2.circle(rgb_frame, (int(pt[0]), int(pt[1])), 5, (0, 0, 255), -1)

        transformed_points = sort_points_by_line(transformed_points)
        # if len(transformed_points) > 10:
        #     # print(f"排序后的像素坐标(x,y): {[f'({p[0]},{p[1]})' for p in transformed_points]}")
        #     # 绘制填充的圆圈作为背景
        #     for i,pt in enumerate(transformed_points):
        #         point_x, point_y = (int(pt[0]), int(pt[1]))
        #        # print(point_x, point_y)
        #         cv2.circle(rgb_frame, (point_x, point_y), 6, (0, 255, 0), -1)
        #         # 在圆圈内绘制距离数字（黑色）
        #         # 计算文字位置（居中）
        #         text_size = cv2.getTextSize(f'{i}', cv2.FONT_HERSHEY_SIMPLEX, 0.2, 1)[0]
        #         text_x = point_x - text_size[0] // 2
        #         text_y = point_y + text_size[1] // 2
        #         cv2.putText(rgb_frame, f'{i}', (text_x, text_y),
        #                     cv2.FONT_HERSHEY_SIMPLEX, 0.2, (0, 0, 0), 1)

        distance_table_path = "pixel_to_physical_py/config/distance_table"
        detection_results = _convert_points_to_detection_results(transformed_points)
        
        valid_results = []

        for i, det_result in enumerate(detection_results):
            physical_processor.process_detection_result(det_result, distance_table_path)
            if det_result.physical_distance > 0:
                valid_results.append((det_result, transformed_points[i]))

        distances = [result[0].physical_distance for result in valid_results]
        print(f"原始距离序列: {[f'{d:.1f}' for d in distances]}")
        print(f"像素坐标(x,y): {[f'({d[1][0]},{d[1][1]})' for d in valid_results]}")
        
        y_coords = [(result[1][0],result[1][1]) for result in valid_results]
        # peaks = find_abnormal_distance_points(distances)  
        peaks, _, _ = detect_anomalous_points(distances, y_coords, distance_threshold=1, y_threshold=1)
        if args.outlier_mode == 'filter':
            # 模式1: 删除异常点
            filtered_results = []
            for i, result in enumerate(valid_results):
                if i not in peaks:
                    filtered_results.append(result)
                else:
                    print(f"删除异常点 {i}: 距离={result[0].physical_distance:.1f}cm")

            print(f"过滤后保留 {len(filtered_results)}/{len(valid_results)} 个点")
        
        # 可视化距离值
        print(f"\n=== 可视化 {len(filtered_results)} 个检测点的距离值 ===")
        for i, (det_result, point_coords) in enumerate(filtered_results):
            point_x, point_y = int(point_coords[0]), int(point_coords[1])
            distance_value = det_result.physical_distance
            

            # 在圆圈内显示距离值
            point_x, point_y = int(point_coords[0]), int(point_coords[1])
            distance_value = int(det_result.physical_distance)  # 取整数，更简洁
            distance_text = f"{distance_value}"

            # distance_text = f"{i}"

            # 根据距离选择颜色
            if distance_value < 30:
                circle_color = (0, 255, 0)  # 绿色 - 近距离
            elif distance_value < 60:
                circle_color = (255, 255, 0)  # 黄色 - 中距离
            else:
                circle_color = (255, 0, 0)  # 红色 - 远距离

            border_color = (255, 255, 255)  # 白色边框

            # 圆圈和字体设置
            circle_radius = 8
            font_scale = 0.3

            # 绘制填充的圆圈作为背景
            cv2.circle(rgb_frame, (point_x, point_y), circle_radius, circle_color, -1)

            # 绘制圆圈边框（修正过的点用不同颜色边框）
            cv2.circle(rgb_frame, (point_x, point_y), circle_radius, border_color, 2)

            # 计算文字位置（居中）
            text_size = cv2.getTextSize(distance_text, cv2.FONT_HERSHEY_SIMPLEX, font_scale, 1)[0]
            text_x = point_x - text_size[0] // 2
            text_y = point_y + text_size[1] // 2

            # 在圆圈内绘制距离数字（黑色）
            cv2.putText(rgb_frame, distance_text, (text_x, text_y),
                        cv2.FONT_HERSHEY_SIMPLEX, font_scale, (0, 0, 0), 1)

            print(f"  点 {i+1}: 位置({point_x}, {point_y}), 距离={distance_value:.1f}cm")

        # 在去畸变图像上绘制范围框
        x_min, x_max = (0, 1280)
        y_min, y_max = (420, 700)
        # 绘制矩形框 (红色，线宽2)
        cv2.rectangle(rgb_frame, (x_min, y_min), (x_max, y_max), (128, 255, 0), 2)
        display_img = cv2.cvtColor(rgb_frame, cv2.COLOR_RGB2BGR)
        cv2.imwrite(f"output/{frame_count}.png", display_img)
        frame_count += 1
        if args.fps_display and frame_count % 10 == 0:
            fps = frame_count / (time.time() - start_time)
            cv2.putText(display_img, f"FPS: {fps:.2f}", (20, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)

        cv2.imshow("监控视角 - 检测结果", display_img)
        if cv2.waitKey(1) == 27:
            break

    cap.release()
    cv2.destroyAllWindows()
    print("程序已结束")

if __name__ == '__main__':
    main()

