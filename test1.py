import numpy as np

# 原始数据
distances = np.array([36.5, 34.4, 33.8, 30.9, 30.9, 31.2, 34.1, 28.2, 27.6, 25.6, 
                      25.0, 24.4, 23.8, 22.9, 22.6, 22.1, 21.8, 21.2, 20.6, 20.3, 
                      20.0, 19.4, 19.4, 19.1, 18.8, 17.9, 16.5, 21.2, 20.6, 19.4, 
                      18.8, 18.2, 17.9, 17.4, 17.4, 17.4, 17.4, 17.4, 17.4, 17.4, 
                      17.4, 17.4, 17.4, 17.4, 17.9, 17.6, 17.9, 17.9, 17.9, 17.9, 
                      18.2, 18.2, 18.2, 18.5, 18.8, 18.8, 19.4, 19.1, 19.4, 19.7, 
                      20.3, 20.6, 20.6, 20.9, 21.8, 22.1, 22.1, 22.9, 24.4, 24.1, 
                      25.6, 27.9, 27.9, 25.9, 25.6, 26.5, 27.9, 29.4, 30.9, 32.4, 
                      33.5, 35.0, 36.5, 39.1, 41.2, 43.8, 45.9, 50.0, 54.7, 59.7, 
                      65.3, 72.4, 74.1])

# 像素坐标 (x, y)
pixel_coords = [
    (872.0, 516.0), (870.0, 526.0), (856.0, 528.0), (858.0, 544.0), (870.0, 544.0),
    (884.0, 542.0), (884.0, 528.0), (868.0, 558.0), (858.0, 560.0), (854.0, 572.0),
    (844.0, 580.0), (836.0, 588.0), (826.0, 594.0), (818.0, 602.0), (808.0, 608.0),
    (796.0, 614.0), (790.0, 618.0), (776.0, 624.0), (764.0, 630.0), (756.0, 634.0),
    (742.0, 638.0), (728.0, 642.0), (714.0, 644.0), (708.0, 648.0), (694.0, 650.0),
    (678.0, 654.0), (662.0, 656.0), (648.0, 658.0), (632.0, 660.0), (616.0, 660.0),
    (612.0, 664.0), (600.0, 662.0), (598.0, 664.0), (582.0, 666.0), (566.0, 666.0),
    (550.0, 668.0), (534.0, 668.0), (518.0, 668.0), (502.0, 668.0), (486.0, 668.0),
    (472.0, 668.0), (456.0, 666.0), (440.0, 666.0), (424.0, 666.0), (422.0, 660.0),
    (408.0, 664.0), (406.0, 660.0), (390.0, 660.0), (374.0, 660.0), (358.0, 660.0),
    (342.0, 658.0), (326.0, 658.0), (310.0, 656.0), (294.0, 654.0), (280.0, 652.0),
    (264.0, 650.0), (260.0, 646.0), (252.0, 648.0), (246.0, 644.0), (230.0, 640.0),
    (216.0, 634.0), (210.0, 630.0), (204.0, 632.0), (198.0, 628.0), (184.0, 620.0),
    (176.0, 614.0), (172.0, 616.0), (168.0, 606.0), (164.0, 590.0), (156.0, 592.0),
    (152.0, 576.0), (146.0, 562.0), (136.0, 562.0), (134.0, 574.0), (170.0, 578.0),
    (178.0, 570.0), (186.0, 562.0), (194.0, 554.0), (202.0, 546.0), (212.0, 538.0),
    (220.0, 532.0), (230.0, 524.0), (238.0, 518.0), (248.0, 510.0), (256.0, 504.0),
    (264.0, 496.0), (274.0, 490.0), (282.0, 482.0), (292.0, 474.0), (298.0, 466.0),
    (308.0, 460.0), (314.0, 452.0), (362.0, 450.0)
]

# 提取 y 值坐标
y_coords = np.array([y for x, y in pixel_coords])

# 计算相邻距离的变化率
distance_changes = np.diff(distances)

# 计算相邻y坐标的变化
y_changes = np.diff(y_coords)

# 计算变化率的均值和标准差
mean_distance_change = np.mean(distance_changes)
std_distance_change = np.std(distance_changes)

# 计算y坐标变化的均值和标准差
mean_y_change = np.mean(y_changes)
std_y_change = np.std(y_changes)

# 设置异常点的阈值
distance_threshold = 1
y_threshold = 2

# 找到异常点：如果距离变化率或y变化大于阈值
outliers = []
for i in range(len(distance_changes)):
    if abs(distance_changes[i] - mean_distance_change) > distance_threshold * std_distance_change or abs(y_changes[i] - mean_y_change) > y_threshold * std_y_change:
        outliers.append(i + 1)  # 异常点是第二个点

print("异常点的位置（基于距离和y坐标变化）：", outliers)
print("异常点的像素坐标：", [pixel_coords[i] for i in outliers])
print("异常点的距离值：", [distances[i] for i in outliers])