import numpy as np
import yaml
import cv2
from typing import Tuple, List, Optional
from dataclasses import dataclass

@dataclass
class CameraCoordinate:
    """相机坐标系中的点"""
    x: float  # 相机坐标系x轴 (水平方向，右为正)
    y: float  # 相机坐标系y轴 (垂直方向，下为正)
    z: float  # 相机坐标系z轴 (深度方向，前为正)
    pixel_x: int  # 原始像素坐标x
    pixel_y: int  # 原始像素坐标y
    confidence: float = 1.0  # 置信度

@dataclass
class OutlierInfo:
    """异常点信息"""
    is_outlier: bool = False
    original_distance: float = 0.0
    corrected_distance: float = 0.0
    correction_method: str = ""

class CameraCoordinateConverter:
    """
    相机坐标转换器
    将像素坐标和距离值转换为相机坐标系(x,y,z)
    """
    
    def __init__(self, camera_config_path: str):
        """
        初始化相机坐标转换器
        
        Args:
            camera_config_path: 相机内参配置文件路径
        """
        self.camera_config_path = camera_config_path
        self.camera_matrix = None
        self.distortion_coeffs = None
        self.image_size = None
        self._load_camera_parameters()
    
    def _load_camera_parameters(self):
        """加载相机内参"""
        try:
            with open(self.camera_config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            camera_params = config['camera_parameters']
            
            # 提取相机内参矩阵
            fx = camera_params['camera_matrix']['fx']
            fy = camera_params['camera_matrix']['fy']
            cx = camera_params['camera_matrix']['cx']
            cy = camera_params['camera_matrix']['cy']
            
            self.camera_matrix = np.array([
                [fx, 0, cx],
                [0, fy, cy],
                [0, 0, 1]
            ], dtype=np.float32)
            
            # 提取畸变系数
            distortion = camera_params['distortion_coefficients']
            self.distortion_coeffs = np.array([
                distortion['radial']['k1'],
                distortion['radial']['k2'],
                distortion['tangential']['p1'],
                distortion['tangential']['p2'],
                distortion['radial']['k3']
            ], dtype=np.float32)
            
            # 图像尺寸
            self.image_size = (
                config['image_properties']['width'],
                config['image_properties']['height']
            )
            
            print(f"✅ 相机参数加载成功:")
            print(f"   fx={fx:.2f}, fy={fy:.2f}, cx={cx:.2f}, cy={cy:.2f}")
            print(f"   图像尺寸: {self.image_size}")
            
        except Exception as e:
            raise RuntimeError(f"加载相机参数失败: {e}")
    
    def pixel_to_camera_coordinate(self, pixel_x: int, pixel_y: int, distance: float, 
                                 confidence: float = 1.0) -> CameraCoordinate:
        """
        将像素坐标和距离转换为相机坐标
        
        Args:
            pixel_x: 像素坐标x
            pixel_y: 像素坐标y  
            distance: 距离值(cm)
            confidence: 置信度
            
        Returns:
            CameraCoordinate: 相机坐标系中的点
        """
        # 提取相机内参
        fx = self.camera_matrix[0, 0]
        fy = self.camera_matrix[1, 1]
        cx = self.camera_matrix[0, 2]
        cy = self.camera_matrix[1, 2]
        
        # 将距离从cm转换为m
        z = distance
        
        # 计算相机坐标系中的x, y坐标
        x = (pixel_x - cx) * z / fx
        y = (pixel_y - cy) * z / fy
        
        return CameraCoordinate(
            x=x, y=y, z=z,
            pixel_x=pixel_x, pixel_y=pixel_y,
            confidence=confidence
        )
    
    def convert_detection_points(self, detection_points: List[Tuple], 
                               distances: List[float]) -> List[CameraCoordinate]:
        """
        批量转换检测点到相机坐标
        
        Args:
            detection_points: 检测点列表 [(x, y, conf), ...]
            distances: 对应的距离列表 [distance, ...]
            
        Returns:
            List[CameraCoordinate]: 相机坐标列表
        """
        if len(detection_points) != len(distances):
            raise ValueError("检测点数量与距离数量不匹配")
        
        camera_coordinates = []
        for (pixel_x, pixel_y, conf), distance in zip(detection_points, distances):
            camera_coord = self.pixel_to_camera_coordinate(
                int(pixel_x), int(pixel_y), distance, conf
            )
            camera_coordinates.append(camera_coord)
        
        return camera_coordinates
    
    def detect_outliers_in_camera_space(self, camera_coordinates: List[CameraCoordinate],
                                      method: str = 'isolation_forest') -> List[OutlierInfo]:
        """
        在相机坐标空间中检测异常点
        
        Args:
            camera_coordinates: 相机坐标列表
            method: 异常检测方法 ('isolation_forest', 'distance_based', 'z_score')
            
        Returns:
            List[OutlierInfo]: 异常点信息列表
        """
        if len(camera_coordinates) < 3:
            return [OutlierInfo() for _ in camera_coordinates]
        
        # 提取坐标数据
        points = np.array([[coord.x, coord.y, coord.z] for coord in camera_coordinates])
        outlier_infos = []
        
        if method == 'isolation_forest':
            outlier_infos = self._detect_outliers_isolation_forest(points, camera_coordinates)
        elif method == 'distance_based':
            outlier_infos = self._detect_outliers_distance_based(points, camera_coordinates)
        elif method == 'z_score':
            outlier_infos = self._detect_outliers_z_score(points, camera_coordinates)
        else:
            raise ValueError(f"不支持的异常检测方法: {method}")
        
        return outlier_infos
    
    def _detect_outliers_isolation_forest(self, points: np.ndarray, 
                                        camera_coordinates: List[CameraCoordinate]) -> List[OutlierInfo]:
        """使用Isolation Forest检测异常点"""
        from sklearn.ensemble import IsolationForest
        
        # 使用3D坐标进行异常检测
        clf = IsolationForest(contamination='auto', random_state=42)
        outlier_labels = clf.fit_predict(points)
        
        outlier_infos = []
        for i, (label, coord) in enumerate(zip(outlier_labels, camera_coordinates)):
            is_outlier = (label == -1)
            outlier_info = OutlierInfo(
                is_outlier=is_outlier,
                original_distance=coord.z * 100,  # 转换回cm
                correction_method="isolation_forest_3d"
            )
            outlier_infos.append(outlier_info)
        
        return outlier_infos
    
    def _detect_outliers_distance_based(self, points: np.ndarray,
                                      camera_coordinates: List[CameraCoordinate]) -> List[OutlierInfo]:
        """基于距离的异常检测"""
        outlier_infos = []
        
        # 计算每个点到其他点的平均距离
        for i, coord in enumerate(camera_coordinates):
            distances_to_others = []
            for j, other_coord in enumerate(camera_coordinates):
                if i != j:
                    dist = np.sqrt(
                        (coord.x - other_coord.x)**2 + 
                        (coord.y - other_coord.y)**2 + 
                        (coord.z - other_coord.z)**2
                    )
                    distances_to_others.append(dist)
            
            avg_distance = np.mean(distances_to_others)
            std_distance = np.std(distances_to_others)
            
            # 如果平均距离超过2个标准差，认为是异常点
            threshold = np.mean(distances_to_others) + 2 * np.std(distances_to_others)
            is_outlier = avg_distance > threshold
            
            outlier_info = OutlierInfo(
                is_outlier=is_outlier,
                original_distance=coord.z * 100,
                correction_method="distance_based_3d"
            )
            outlier_infos.append(outlier_info)
        
        return outlier_infos
    
    def _detect_outliers_z_score(self, points: np.ndarray,
                                camera_coordinates: List[CameraCoordinate]) -> List[OutlierInfo]:
        """基于Z-score的异常检测"""
        outlier_infos = []
        
        # 分别对x, y, z坐标计算Z-score
        z_scores = np.abs((points - np.mean(points, axis=0)) / np.std(points, axis=0))
        
        # 如果任一坐标的Z-score超过阈值，认为是异常点
        threshold = 2.0
        for i, (z_score, coord) in enumerate(zip(z_scores, camera_coordinates)):
            is_outlier = np.any(z_score > threshold)
            
            outlier_info = OutlierInfo(
                is_outlier=is_outlier,
                original_distance=coord.z * 100,
                correction_method="z_score_3d"
            )
            outlier_infos.append(outlier_info)
        
        return outlier_infos
    
    def correct_outliers(self, camera_coordinates: List[CameraCoordinate],
                        outlier_infos: List[OutlierInfo],
                        correction_method: str = 'neighbor_average') -> List[CameraCoordinate]:
        """
        修正异常点
        
        Args:
            camera_coordinates: 原始相机坐标列表
            outlier_infos: 异常点信息列表
            correction_method: 修正方法 ('neighbor_average', 'interpolation')
            
        Returns:
            List[CameraCoordinate]: 修正后的相机坐标列表
        """
        corrected_coordinates = camera_coordinates.copy()
        
        for i, (coord, outlier_info) in enumerate(zip(camera_coordinates, outlier_infos)):
            if outlier_info.is_outlier:
                if correction_method == 'neighbor_average':
                    corrected_coord = self._correct_by_neighbor_average(
                        i, camera_coordinates, outlier_infos
                    )
                elif correction_method == 'interpolation':
                    corrected_coord = self._correct_by_interpolation(
                        i, camera_coordinates, outlier_infos
                    )
                else:
                    corrected_coord = coord
                
                corrected_coordinates[i] = corrected_coord
                
                # 更新异常信息
                outlier_info.corrected_distance = corrected_coord.z * 100
        
        return corrected_coordinates
    
    def _correct_by_neighbor_average(self, outlier_index: int,
                                   camera_coordinates: List[CameraCoordinate],
                                   outlier_infos: List[OutlierInfo],
                                   window_size: int = 2) -> CameraCoordinate:
        """使用邻居平均值修正异常点"""
        valid_neighbors = []
        
        # 向前搜索
        for i in range(max(0, outlier_index - window_size), outlier_index):
            if not outlier_infos[i].is_outlier:
                valid_neighbors.append(camera_coordinates[i])
        
        # 向后搜索
        for i in range(outlier_index + 1, min(len(camera_coordinates), outlier_index + window_size + 1)):
            if not outlier_infos[i].is_outlier:
                valid_neighbors.append(camera_coordinates[i])
        
        if valid_neighbors:
            # 计算平均坐标
            avg_x = np.mean([coord.x for coord in valid_neighbors])
            avg_y = np.mean([coord.y for coord in valid_neighbors])
            avg_z = np.mean([coord.z for coord in valid_neighbors])
            
            original_coord = camera_coordinates[outlier_index]
            return CameraCoordinate(
                x=avg_x, y=avg_y, z=avg_z,
                pixel_x=original_coord.pixel_x,
                pixel_y=original_coord.pixel_y,
                confidence=original_coord.confidence
            )
        else:
            return camera_coordinates[outlier_index]
    
    def _correct_by_interpolation(self, outlier_index: int,
                                camera_coordinates: List[CameraCoordinate],
                                outlier_infos: List[OutlierInfo]) -> CameraCoordinate:
        """使用插值方法修正异常点"""
        # 简单的线性插值实现
        # 找到前后最近的有效点
        prev_valid = None
        next_valid = None
        
        # 向前搜索
        for i in range(outlier_index - 1, -1, -1):
            if not outlier_infos[i].is_outlier:
                prev_valid = (i, camera_coordinates[i])
                break
        
        # 向后搜索
        for i in range(outlier_index + 1, len(camera_coordinates)):
            if not outlier_infos[i].is_outlier:
                next_valid = (i, camera_coordinates[i])
                break
        
        if prev_valid and next_valid:
            # 线性插值
            prev_idx, prev_coord = prev_valid
            next_idx, next_coord = next_valid
            
            weight = (outlier_index - prev_idx) / (next_idx - prev_idx)
            
            interp_x = prev_coord.x + weight * (next_coord.x - prev_coord.x)
            interp_y = prev_coord.y + weight * (next_coord.y - prev_coord.y)
            interp_z = prev_coord.z + weight * (next_coord.z - prev_coord.z)
            
            original_coord = camera_coordinates[outlier_index]
            return CameraCoordinate(
                x=interp_x, y=interp_y, z=interp_z,
                pixel_x=original_coord.pixel_x,
                pixel_y=original_coord.pixel_y,
                confidence=original_coord.confidence
            )
        else:
            return camera_coordinates[outlier_index]
