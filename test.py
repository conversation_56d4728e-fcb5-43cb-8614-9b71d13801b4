# # # import numpy as np

# # # def correct_outliers(distance_list, multiplier=1.5, window=8):
# # #     """
# # #     识别并校正距离序列中的异常值（基于IQR + 周边均值校正）

# # #     参数:
# # #         distance_list (list of str): 原始距离字符串列表
# # #         multiplier (float): IQR乘数，控制异常检测灵敏度
# # #         window (int): 用于校正的前后窗口大小（非异常点）

# # #     返回:
# # #         corrected (list of float): 校正后的距离列表
# # #         outlier_indices (list of int): 被识别为异常的索引位置
# # #     """
# # #     data = np.array([float(x) for x in distance_list])
# # #     corrected = data.copy()

# # #     # 计算 IQR 异常阈值
# # #     Q1 = np.percentile(data, 25)
# # #     Q3 = np.percentile(data, 75)
# # #     IQR = Q3 - Q1
# # #     lower_bound = Q1 - multiplier * IQR
# # #     upper_bound = Q3 + multiplier * IQR

# # #     # 找出异常值索引
# # #     outlier_indices = np.where((data < lower_bound) | (data > upper_bound))[0]

# # #     # 逐个校正异常点
# # #     for idx in outlier_indices:
# # #         # 向前、向后查找非异常值
# # #         neighbors = []

# # #         # 前向查找
# # #         i = idx - 1
# # #         while i >= 0 and len(neighbors) < window:
# # #             if lower_bound <= data[i] <= upper_bound:
# # #                 neighbors.append(data[i])
# # #             i -= 1

# # #         # 后向查找
# # #         i = idx + 1
# # #         while i < len(data) and len(neighbors) < 2 * window:
# # #             if lower_bound <= data[i] <= upper_bound:
# # #                 neighbors.append(data[i])
# # #             i += 1

# # #         # 计算邻居均值校正
# # #         if neighbors:
# # #             corrected[idx] = np.mean(neighbors)

# # #     return corrected.tolist(), outlier_indices.tolist()


# # # raw_data = ['22.6', '22.6', '24.1', '21.5', '24.4', '20.9', '25.0', '20.3', '25.6', '20.0',
# # #             '26.5', '19.4', '27.1', '19.1', '27.9', '18.8', '28.5', '18.5', '29.4', '18.2',
# # #             '29.7', '30.6', '17.9', '17.1', '31.2', '18.2', '17.1', '16.2', '32.1', '18.2',
# # #             '17.1', '16.2', '32.4', '18.2', '17.1', '16.2', '32.6', '18.5', '17.1', '33.5',
# # #             '16.2', '19.4', '34.4', '19.4', '18.5', '17.1', '16.2', '35.0', '18.2', '17.1',
# # #             '16.2', '35.6', '16.8', '16.2', '36.5', '16.8', '37.9', '16.2', '39.4', '16.8',
# # #             '16.2', '40.9', '15.9', '15.9', '42.4', '15.9', '15.9', '43.8', '45.9', '47.9',
# # #             '51.2', '55.6', '57.9', '67.1', '72.4']

# # # corrected_values, outlier_idxs = correct_outliers(raw_data)

# # # print("异常点位置:", outlier_idxs)
# # # print("校正后结果（末尾10项）:", corrected_values[-10:])




# # import numpy as np
# # from scipy.signal import find_peaks
# # import matplotlib.pyplot as plt

# # # 原始距离序列
# # original_sequence = ['43.8', '39.7', '46.8', '38.2', '49.1', '51.2', '37.6', '53.5', '36.8', '55.9', '36.8', '56.8', '35.9', '57.9', '59.7', '35.9', '61.5', '36.8', '63.5', '36.5', '63.5', '36.5', '63.5', '37.4', '65.3', '37.1', '65.3', '37.9', '67.1', '37.9', '72.4', '67.1', '37.9', '72.4', '72.4', '67.1', '38.5', '72.4', '67.1', '38.5', '67.1', '38.5', '65.3', '38.5', '74.1', '63.5', '37.9', '54.7', '63.5', '51.5', '37.9', '47.1', '49.4', '63.5', '37.1', '45.9', '65.3', '45.3', '37.4', '37.6', '51.5', '40.3', '68.8', '72.4', '41.2', '55.3', '70.6', '72.4', '52.6', '36.2', '54.7', '72.4', '34.4', '61.5', '70.6', '63.5', '33.5', '28.2', '31.2', '25.6', '33.2', '28.2', '25.6', '31.2', '32.9', '31.2', '27.6', '25.6']

# # # 转换为浮点数列表
# # distances = [float(x) for x in original_sequence]

# # # 方法1：滑动窗口局部阈值检测
# # def detect_bulges_sliding_window(data, window_size=5, threshold_ratio=1.3):
# #     bulges = []
# #     for i in range(len(data)):
# #         left = max(0, i - window_size)
# #         right = min(len(data), i + window_size)
# #         window = data[left:right]
# #         local_mean = np.mean(window)
# #         if data[i] > local_mean * threshold_ratio:
# #             bulges.append((i, data[i]))  # 返回(索引, 距离值)
# #     return bulges

# # # 方法2：峰值检测
# # def detect_bulges_peaks(data, prominence=10):
# #     peaks, _ = find_peaks(data, prominence=prominence)
# #     return [(i, data[i]) for i in peaks]

# # # 检测隆起部分
# # bulges_window = detect_bulges_sliding_window(distances, window_size=10, threshold_ratio=1.3)
# # bulges_peaks = detect_bulges_peaks(distances, prominence=10)

# # # 合并结果（取并集）
# # all_bulges = list(set(bulges_window + bulges_peaks))
# # all_bulges.sort()  # 按索引排序

# # # 可视化
# # plt.figure(figsize=(15, 6))
# # plt.plot(distances, 'b-', label='list', linewidth=1)

# # # 标记滑动窗口检测结果
# # plt.scatter(
# #     [x[0] for x in bulges_window],
# #     [x[1] for x in bulges_window],
# #     color='red',
# #     s=50,
# #     label='window'
# # )

# # # 标记峰值检测结果
# # plt.scatter(
# #     [x[0] for x in bulges_peaks],
# #     [x[1] for x in bulges_peaks],
# #     color='green',
# #     marker='x',
# #     s=100,
# #     linewidths=2,
# #     label='top'
# # )

# # plt.title('Wire隆起部分检测', fontsize=14)
# # plt.xlabel('数据点索引', fontsize=12)
# # plt.ylabel('距离值', fontsize=12)
# # plt.legend(fontsize=10)
# # plt.grid(True, linestyle='--', alpha=0.7)
# # plt.tight_layout()

# # # 打印检测结果
# # print("检测到的隆起点（索引, 距离值）：")
# # for idx, val in all_bulges:
# #     print(f"索引 {idx}: {val}")

# # plt.show()



# sorted_coords = [
#     (770.0, 496.0), (766.0, 492.0), (770.0, 484.0), (768.0, 504.0), (758.0, 508.0), (758.0, 482.0), 
#     (744.0, 476.0), (742.0, 512.0), (728.0, 516.0), (728.0, 472.0), (724.0, 468.0), (722.0, 520.0), 
#     (716.0, 518.0), (710.0, 522.0), (710.0, 468.0), (712.0, 472.0), (694.0, 526.0), (694.0, 470.0), 
#     (680.0, 528.0), (680.0, 470.0), (676.0, 472.0), (676.0, 540.0), (664.0, 542.0), (664.0, 528.0), 
#     (666.0, 468.0), (660.0, 556.0), (660.0, 472.0), (648.0, 558.0), (648.0, 542.0), (648.0, 526.0), 
#     (648.0, 514.0), (646.0, 472.0), (646.0, 558.0), (632.0, 558.0), (632.0, 542.0), (632.0, 528.0), 
#     (630.0, 474.0), (616.0, 558.0), (616.0, 544.0), (616.0, 530.0), (614.0, 476.0), (604.0, 554.0), 
#     (602.0, 544.0), (598.0, 476.0), (582.0, 478.0), (566.0, 480.0), (550.0, 482.0), (536.0, 484.0), 
#     (520.0, 484.0), (504.0, 484.0), (488.0, 484.0), (470.0, 482.0), (454.0, 482.0), (438.0, 480.0), 
#     (422.0, 478.0), (408.0, 474.0), (390.0, 466.0), (376.0, 460.0), (362.0, 456.0), (356.0, 452.0)
# ]


# import matplotlib.pyplot as plt

# x = [point[0] for point in sorted_coords]
# y = [-point[1] for point in sorted_coords]  # 翻转 y 轴以匹配图像坐标系

# plt.figure(figsize=(10, 6))
# plt.plot(x, y, 'b-', marker='o', markersize=3)
# plt.xlabel('X')
# plt.ylabel('Y (inverted)')
# plt.title('Connected Path of Points')
# plt.grid()
# plt.show()



# def find_non_smooth_segments(distance_sequence):
#     # 将字符串序列转换为浮点数
#     distances = [float(d) for d in distance_sequence]
    
#     # 计算一阶差分（梯度）
#     first_diff = [distances[i] - distances[i-1] for i in range(1, len(distances))]
    
#     # 计算二阶差分（梯度的梯度）
#     second_diff = [first_diff[i] - first_diff[i-1] for i in range(1, len(first_diff))]
    
#     # 设定二阶差分的阈值（可以根据实际情况调整）
#     threshold = 5.0  # 假设二阶差分绝对值超过5.0为不平滑
    
#     # 找出不平滑的点（二阶差分绝对值大于阈值）
#     non_smooth_indices = [i+1 for i in range(len(second_diff)) if abs(second_diff[i]) > threshold]
    
#     # 将不平滑的点分组为连续的段
#     segments = []
#     if non_smooth_indices:
#         current_segment = [non_smooth_indices[0]]
#         for i in range(1, len(non_smooth_indices)):
#             if non_smooth_indices[i] == non_smooth_indices[i-1] + 1:
#                 current_segment.append(non_smooth_indices[i])
#             else:
#                 segments.append(current_segment)
#                 current_segment = [non_smooth_indices[i]]
#         segments.append(current_segment)
    
#     return segments, first_diff, second_diff

# distance_sequence = ['23.5', '23.2', '23.2', '23.8', '24.1', '24.4', '25.0', '25.0', '25.6', '25.9', '26.8', '27.1', '27.6', '28.5', '29.4', '29.7', '30.3', '30.9', '31.5', '31.8', '33.2', '33.8', '34.4', '37.4', '39.4', '45.9', '50.0', '54.7', '57.9', '65.3', '72.4', '44.4', '42.4', '40.3', '38.8', '36.8', '35.6', '34.7', '35.6', '33.2', '33.2', '32.1', '31.2', '30.3', '29.7', '29.4', '28.8', '28.2', '27.4', '27.1', '26.2', '26.8', '25.9', '25.9', '25.3', '25.0', '24.7', '24.4', '23.8', '23.2', '23.5', '22.9', '22.4', '21.8', '20.9', '20.3', '19.7', '18.8', '18.2', '17.1', '17.1', '15.9', '15.9', '15.9', '26.8', '28.2', '34.7', '34.1', '32.1', '31.2', '34.1', '31.2', '31.2', '65.3', '22.6', '22.4', '21.8', '21.2', '20.3', '19.4', '18.5', '17.1', '16.5', '15.9', '15.9', '15.9']

# segments, first_diff, second_diff = find_non_smooth_segments(distance_sequence)

# print("不平滑的点串（索引从0开始）：")
# for seg in segments:
#     print(f"段: {seg}, 对应的距离值: {[distance_sequence[i] for i in seg]}")

# print("\n一阶差分（梯度）:")
# print(first_diff)

# print("\n二阶差分（梯度的梯度）:")
# print(second_diff)


def find_abnormal_points(distance_sequence):
    distances = [float(d) for d in distance_sequence]
    abnormal_indices = []
    
    # 计算移动平均值和标准差
    window_size = 5
    for i in range(len(distances)):
        start = max(0, i - window_size)
        end = min(len(distances), i + window_size + 1)
        window = distances[start:end]
        mean = sum(window) / len(window)
        std = (sum((x - mean)**2 for x in window)/len(window))**0.5
        
        # 标记超过3个标准差的点为异常
        if abs(distances[i] - mean) > 3 * std:
            abnormal_indices.append(i)
    
    return abnormal_indices

distance_sequence = ['68.8','70.6','65.3','59.7','54.7','51.5','48.2','45.9','43.2','41.2','39.7','36.8','35.6','35.0','33.5','32.6','32.1','30.6','29.7','29.1','27.6','26.8','25.9','25.6','25.0','25.3','24.4','23.8','23.2','22.6','21.8','21.2','20.3','19.7','18.5','17.1','16.2','16.2']
abnormal_points = find_abnormal_points(distance_sequence)

print("所有异常值索引及数值：")
for idx in abnormal_points:
    print(f"索引 {idx}: {distance_sequence[idx]}")


# 计算前10点和后续点的平均变化率
first_part = [float(x) for x in distance_sequence[:10]]
later_part = [float(x) for x in distance_sequence[10:]]

print("前10点平均变化:", sum(first_part[i]-first_part[i+1] for i in range(9))/9)
print("后续点平均变化:", sum(later_part[i]-later_part[i+1] for i in range(len(later_part)-1))/(len(later_part)-1))